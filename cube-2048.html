<!DOCTYPE html>
<html>
<head>
    <title>2048 3D Kostka - FINAL</title>
    <style>
        body {
            margin: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        canvas { display: block; }
        #ui {
            position: absolute; top: 10px; left: 10px; z-index: 100;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div id="ui">
        <h3>2048 3D Kostka Rubika</h3>
        <div>Wynik: <span id="score">0</span></div>
        <div>Status: <span id="status">Ładowanie...</span></div>
        <div style="margin-top: 10px; font-size: 11px;">
            <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej <PERSON>ie</div>
            <div><strong>🖱️ Mysz</strong> - obrót kostki (przeciągnij)</div>
            <div><strong>🔄 Warstwy Rubika:</strong></div>
            <div style="margin-left: 10px; font-size: 10px;">
                <div>• <strong>Lewy klik</strong> na kostce - zaznacz warstwę</div>
                <div>• <strong>Prawy klik</strong> na kostce - przełącz typ warstwy</div>
                <div>• <strong>Klik na strzałki</strong> - obróć zaznaczoną warstwę</div>
            </div>
        </div>

        <div id="layerInfo" style="margin-top: 10px; padding: 10px; background: rgba(255,255,0,0.2); border-radius: 5px; display: none;">
            <div style="font-weight: bold;">Zaznaczona warstwa:</div>
            <div id="layerDetails">Kliknij na kostkę żeby zaznaczyć warstwę</div>
        </div>
    </div>

    <!-- Podglądy ścian -->
    <div id="facePreview" style="position: absolute; top: 10px; right: 10px; z-index: 100;">
        <div style="background: rgba(255,255,255,0.95); padding: 15px; border-radius: 15px; color: #333; box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
            <h4 style="margin: 0 0 10px 0; text-align: center; color: #2c3e50;">Ściany kostki</h4>
            <div id="faceGrid" style="display: grid; grid-template-columns: repeat(3, 60px); gap: 5px; justify-content: center;">
                <!-- Ściany będą dodane dynamicznie -->
            </div>
            <div style="margin-top: 10px; text-align: center; font-size: 12px;">
                <div>Aktywna: <span id="activeFaceDisplay" style="font-weight: bold; color: #ffff99;">FRONT</span></div>
                <div style="margin-top: 5px;">Kliknij na podgląd żeby przełączyć</div>
            </div>
        </div>
    </div>

    <!-- Kontrolki obrotów warstw -->
    <div id="layerControls" style="position: absolute; bottom: 10px; right: 10px; z-index: 100; display: none;">
        <div style="background: rgba(255,255,255,0.95); padding: 15px; border-radius: 15px; color: #333; box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
            <h4 style="margin: 0 0 10px 0; text-align: center; color: #2c3e50;">Obrót warstwy</h4>
            <div style="display: flex; gap: 10px; justify-content: center;">
                <button id="rotateLeft" style="padding: 12px 16px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; cursor: pointer; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">↺ Lewo</button>
                <button id="rotateRight" style="padding: 12px 16px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none; border-radius: 10px; cursor: pointer; box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(245, 87, 108, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(245, 87, 108, 0.4)'">↻ Prawo</button>
            </div>
            <div style="text-align: center; margin-top: 10px; font-size: 12px;">
                <button id="clearSelection" style="padding: 8px 12px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; border: none; border-radius: 8px; cursor: pointer; box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(252, 182, 159, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(252, 182, 159, 0.4)'">Wyczyść zaznaczenie</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        const status = document.getElementById('status');
        
        function updateStatus(msg) {
            console.log(msg);
            status.innerHTML = msg;
        }
        
        updateStatus('Uruchamianie...');
        
        if (typeof THREE === 'undefined') {
            updateStatus('BŁĄD: THREE.js nie załadowane!');
        } else {
            updateStatus('THREE.js OK');
            
            // Zmienne globalne
            let scene, camera, renderer, cubeGroup;
            let gameData = {};
            let tiles = {};
            let activeFace = 'front';
            let score = 0;

            // Nowe zmienne dla funkcjonalności
            let selectedLayer = null;
            let layerType = 'horizontal'; // 'horizontal', 'vertical', 'depth'
            let facePreviewCanvases = {};
            let raycaster, mouse;
            
            // Kolory ścian - nowoczesne gradienty
            const faceColors = {
                front: '#667eea',   back: '#f093fb',   left: '#4facfe',
                right: '#43e97b',   top: '#fa709a',    bottom: '#ffecd2'
            };

            // Kolory gradientów dla ścian
            const faceGradients = {
                front: ['#667eea', '#764ba2'],   back: ['#f093fb', '#f5576c'],   left: ['#4facfe', '#00f2fe'],
                right: ['#43e97b', '#38f9d7'],   top: ['#fa709a', '#fee140'],    bottom: ['#ffecd2', '#fcb69f']
            };
            
            // Kolory cyfr - nowoczesne gradienty
            const numberGradients = {
                2: ['#667eea', '#764ba2'], 4: ['#f093fb', '#f5576c'], 8: ['#4facfe', '#00f2fe'], 16: ['#43e97b', '#38f9d7'],
                32: ['#fa709a', '#fee140'], 64: ['#ff9a9e', '#fecfef'], 128: ['#a8edea', '#fed6e3'], 256: ['#ffecd2', '#fcb69f'],
                512: ['#ff8a80', '#ea4c89'], 1024: ['#667eea', '#764ba2'], 2048: ['#f093fb', '#f5576c']
            };

            function getTextColor(value) {
                return '#ffffff'; // Zawsze biały tekst na gradientach
            }
            
            function createTileTexture(value, face) {
                const canvas = document.createElement('canvas');
                canvas.width = 512;
                canvas.height = 512;
                const ctx = canvas.getContext('2d');

                // Gradient tła ściany
                const faceGrad = faceGradients[face];
                const bgGradient = ctx.createLinearGradient(0, 0, 512, 512);
                bgGradient.addColorStop(0, faceGrad[0]);
                bgGradient.addColorStop(1, faceGrad[1]);

                // Zaokrąglone tło ściany
                ctx.fillStyle = bgGradient;
                roundRect(ctx, 0, 0, 512, 512, 20);
                ctx.fill();

                // Subtelna ramka
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 3;
                roundRect(ctx, 1.5, 1.5, 509, 509, 20);
                ctx.stroke();

                // Jeśli jest cyfra
                if (value > 0) {
                    const numberGrad = numberGradients[value] || ['#667eea', '#764ba2'];
                    const textColor = getTextColor(value);

                    // Kafelek z gradientem
                    const margin = 30;
                    const tileSize = 512 - 2*margin;

                    const tileGradient = ctx.createLinearGradient(margin, margin, margin + tileSize, margin + tileSize);
                    tileGradient.addColorStop(0, numberGrad[0]);
                    tileGradient.addColorStop(1, numberGrad[1]);

                    ctx.fillStyle = tileGradient;
                    roundRect(ctx, margin, margin, tileSize, tileSize, 25);
                    ctx.fill();

                    // Cień kafelka
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 5;

                    // Highlight na górze kafelka
                    const highlightGradient = ctx.createLinearGradient(margin, margin, margin, margin + tileSize/3);
                    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
                    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                    ctx.fillStyle = highlightGradient;
                    roundRect(ctx, margin, margin, tileSize, tileSize/3, 25);
                    ctx.fill();

                    // Reset cienia
                    ctx.shadowColor = 'transparent';

                    // Cyfra z cieniem
                    const fontSize = value >= 1000 ? 100 : value >= 100 ? 120 : 140;
                    ctx.font = `bold ${fontSize}px 'Segoe UI', Arial, sans-serif`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';

                    // Cień tekstu
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                    ctx.fillText(value.toString(), 258, 260);

                    // Główny tekst
                    ctx.fillStyle = textColor;
                    ctx.fillText(value.toString(), 256, 256);
                } else {
                    // Puste pole - subtelny wzór
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                    const margin = 30;
                    const tileSize = 512 - 2*margin;
                    roundRect(ctx, margin, margin, tileSize, tileSize, 25);
                    ctx.fill();

                    // Subtelna ramka pustego pola
                    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                    ctx.lineWidth = 2;
                    roundRect(ctx, margin, margin, tileSize, tileSize, 25);
                    ctx.stroke();
                }

                return new THREE.CanvasTexture(canvas);
            }

            // Funkcja pomocnicza do zaokrąglonych prostokątów
            function roundRect(ctx, x, y, width, height, radius) {
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + width - radius, y);
                ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                ctx.lineTo(x + width, y + height - radius);
                ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                ctx.lineTo(x + radius, y + height);
                ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
                ctx.lineTo(x, y + radius);
                ctx.quadraticCurveTo(x, y, x + radius, y);
                ctx.closePath();
            }
            
            try {
                updateStatus('Tworzenie sceny...');
                
                // Scena
                scene = new THREE.Scene();
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                renderer = new THREE.WebGLRenderer({ antialias: true });
                
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0xf5f7fa); // Jasne tło pasujące do gradientu
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.body.appendChild(renderer.domElement);
                
                updateStatus('Dodawanie światła...');
                
                // Światło - lepsze oświetlenie
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                scene.add(directionalLight);

                // Dodatkowe światło wypełniające
                const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
                fillLight.position.set(-5, -5, -5);
                scene.add(fillLight);
                
                // Grupa kostki
                cubeGroup = new THREE.Group();
                scene.add(cubeGroup);
                
                updateStatus('Inicjalizacja danych...');
                
                // Inicjalizuj dane gry
                Object.keys(faceColors).forEach(face => {
                    gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
                    tiles[face] = [];
                });
                
                // Dodaj początkowe cyfry
                gameData.front[0][0] = 2;
                gameData.front[2][2] = 4;
                gameData.top[1][1] = 8;
                
                updateStatus('Tworzenie kostki...');
                
                // Stwórz kostkę
                Object.keys(faceColors).forEach(face => {
                    tiles[face] = [];
                    for (let x = 0; x < 3; x++) {
                        tiles[face][x] = [];
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const texture = createTileTexture(value, face);

                            const geometry = new THREE.PlaneGeometry(1.5, 1.5);
                            const material = new THREE.MeshLambertMaterial({
                                map: texture,
                                transparent: true,
                                side: THREE.DoubleSide
                            });

                            const tile = new THREE.Mesh(geometry, material);
                            tile.castShadow = true;
                            tile.receiveShadow = true;

                            // Pozycjonowanie
                            const posX = (x - 1) * 1.8;
                            const posY = (1 - y) * 1.8;
                            const offset = 2.7;

                            switch(face) {
                                case 'front':
                                    tile.position.set(posX, posY, offset);
                                    break;
                                case 'back':
                                    tile.position.set(-posX, posY, -offset);
                                    tile.rotation.y = Math.PI;
                                    break;
                                case 'right':
                                    tile.position.set(offset, posY, -posX);
                                    tile.rotation.y = Math.PI/2;
                                    break;
                                case 'left':
                                    tile.position.set(-offset, posY, posX);
                                    tile.rotation.y = -Math.PI/2;
                                    break;
                                case 'top':
                                    tile.position.set(posX, offset, posY);
                                    tile.rotation.x = -Math.PI/2;
                                    break;
                                case 'bottom':
                                    tile.position.set(posX, -offset, -posY);
                                    tile.rotation.x = Math.PI/2;
                                    break;
                            }

                            tiles[face][x][y] = tile;
                            cubeGroup.add(tile);
                        }
                    }
                });
                
                updateStatus('Ustawianie kamery...');
                
                // Pozycja kamery
                camera.position.set(8, 6, 8);
                camera.lookAt(0, 0, 0);
                
                updateStatus('Dodawanie kontrolek...');
                
                // Obsługa myszy
                let isDragging = false;
                let lastMouseX = 0, lastMouseY = 0;
                
                renderer.domElement.addEventListener('mousedown', (event) => {
                    isDragging = true;
                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });
                
                renderer.domElement.addEventListener('mousemove', (event) => {
                    if (!isDragging) return;
                    
                    const deltaX = event.clientX - lastMouseX;
                    const deltaY = event.clientY - lastMouseY;
                    
                    cubeGroup.rotation.y += deltaX * 0.01;
                    cubeGroup.rotation.x += deltaY * 0.01;
                    
                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });
                
                renderer.domElement.addEventListener('mouseup', (event) => {
                    if (isDragging) {
                        isDragging = false;
                    } else {
                        // Klik bez przeciągania - obsługa zaznaczania warstw
                        handleTileClick(event);
                    }
                });

                // Obsługa prawego kliku
                renderer.domElement.addEventListener('contextmenu', (event) => {
                    event.preventDefault();
                    toggleLayerType();
                });

                // Obsługa klawiatury dla gry 2048
                document.addEventListener('keydown', (event) => {
                    switch(event.key) {
                        case 'ArrowLeft':
                            event.preventDefault();
                            moveLeft();
                            break;
                        case 'ArrowRight':
                            event.preventDefault();
                            moveRight();
                            break;
                        case 'ArrowUp':
                            event.preventDefault();
                            moveUp();
                            break;
                        case 'ArrowDown':
                            event.preventDefault();
                            moveDown();
                            break;
                        case '1': case '2': case '3': case '4': case '5': case '6':
                            switchToFace(Object.keys(faceColors)[parseInt(event.key) - 1]);
                            break;
                    }
                });

                updateStatus('Dodawanie funkcji gry...');

                // Inicjalizuj raycaster i mouse
                raycaster = new THREE.Raycaster();
                mouse = new THREE.Vector2();

                // Inicjalizuj podglądy ścian
                createFacePreviews();

                // Inicjalizuj kontrolki warstw
                setupLayerControls();

                updateStatus('Uruchamianie animacji...');
                
                // Animacja
                function animate() {
                    requestAnimationFrame(animate);
                    renderer.render(scene, camera);
                }
                animate();
                
                updateStatus('GOTOWE - kostka powinna być widoczna!');

            } catch (error) {
                updateStatus('BŁĄD: ' + error.message);
                console.error('Błąd inicjalizacji:', error);
            }

            // ===== FUNKCJE GRY 2048 =====

            function updateTiles() {
                Object.keys(faceColors).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const texture = createTileTexture(value, face);
                            tiles[face][x][y].material.map = texture;
                            tiles[face][x][y].material.needsUpdate = true;
                        }
                    }
                });
                updateFacePreviews();
                document.getElementById('score').textContent = score;
            }

            function moveLeft() {
                let moved = false;
                const face = activeFace;

                for (let y = 0; y < 3; y++) {
                    const row = [];
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][x][y] !== 0) {
                            row.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < row.length - 1; i++) {
                        if (row[i] === row[i + 1]) {
                            row[i] *= 2;
                            score += row[i];
                            row.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z prawej
                    while (row.length < 3) {
                        row.push(0);
                    }

                    // Sprawdź czy coś się zmieniło
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][x][y] !== row[x]) {
                            moved = true;
                        }
                        gameData[face][x][y] = row[x];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveRight() {
                let moved = false;
                const face = activeFace;

                for (let y = 0; y < 3; y++) {
                    const row = [];
                    for (let x = 2; x >= 0; x--) {
                        if (gameData[face][x][y] !== 0) {
                            row.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < row.length - 1; i++) {
                        if (row[i] === row[i + 1]) {
                            row[i] *= 2;
                            score += row[i];
                            row.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z lewej
                    while (row.length < 3) {
                        row.push(0);
                    }

                    // Sprawdź czy coś się zmieniło i ustaw od prawej
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][2-x][y] !== row[x]) {
                            moved = true;
                        }
                        gameData[face][2-x][y] = row[x];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveUp() {
                let moved = false;
                const face = activeFace;

                for (let x = 0; x < 3; x++) {
                    const col = [];
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][y] !== 0) {
                            col.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < col.length - 1; i++) {
                        if (col[i] === col[i + 1]) {
                            col[i] *= 2;
                            score += col[i];
                            col.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z dołu
                    while (col.length < 3) {
                        col.push(0);
                    }

                    // Sprawdź czy coś się zmieniło
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][y] !== col[y]) {
                            moved = true;
                        }
                        gameData[face][x][y] = col[y];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveDown() {
                let moved = false;
                const face = activeFace;

                for (let x = 0; x < 3; x++) {
                    const col = [];
                    for (let y = 2; y >= 0; y--) {
                        if (gameData[face][x][y] !== 0) {
                            col.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < col.length - 1; i++) {
                        if (col[i] === col[i + 1]) {
                            col[i] *= 2;
                            score += col[i];
                            col.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z góry
                    while (col.length < 3) {
                        col.push(0);
                    }

                    // Sprawdź czy coś się zmieniło i ustaw od dołu
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][2-y] !== col[y]) {
                            moved = true;
                        }
                        gameData[face][x][2-y] = col[y];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function addRandomTile() {
                const emptyTiles = [];

                // Zbierz puste pola ze wszystkich ścian
                Object.keys(faceColors).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            if (gameData[face][x][y] === 0) {
                                emptyTiles.push({face, x, y});
                            }
                        }
                    }
                });

                if (emptyTiles.length > 0) {
                    const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
                    const value = Math.random() < 0.9 ? 2 : 4;
                    gameData[randomTile.face][randomTile.x][randomTile.y] = value;
                    updateTiles();
                }
            }

            // ===== FUNKCJE PODGLĄDÓW ŚCIAN =====

            function createFacePreviews() {
                const faceGrid = document.getElementById('faceGrid');
                const faceNames = Object.keys(faceColors);

                faceNames.forEach((face, index) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 60;
                    canvas.height = 60;
                    canvas.style.border = '2px solid #333';
                    canvas.style.borderRadius = '5px';
                    canvas.style.cursor = 'pointer';
                    canvas.title = face.toUpperCase();

                    canvas.addEventListener('click', () => switchToFace(face));

                    facePreviewCanvases[face] = canvas;
                    faceGrid.appendChild(canvas);
                });

                updateFacePreviews();
            }

            function updateFacePreviews() {
                Object.keys(faceColors).forEach(face => {
                    const canvas = facePreviewCanvases[face];
                    const ctx = canvas.getContext('2d');

                    // Wyczyść canvas
                    ctx.clearRect(0, 0, 60, 60);

                    // Tło ściany
                    ctx.fillStyle = faceColors[face];
                    ctx.fillRect(0, 0, 60, 60);

                    // Siatka 3x3
                    const tileSize = 18;
                    const margin = 3;

                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const posX = margin + x * (tileSize + 1);
                            const posY = margin + y * (tileSize + 1);

                            if (value > 0) {
                                const numberGrad = numberGradients[value] || ['#667eea', '#764ba2'];
                                const gradient = ctx.createLinearGradient(posX, posY, posX + tileSize, posY + tileSize);
                                gradient.addColorStop(0, numberGrad[0]);
                                gradient.addColorStop(1, numberGrad[1]);
                                ctx.fillStyle = gradient;
                                ctx.fillRect(posX, posY, tileSize, tileSize);

                                // Cyfra
                                const fontSize = value >= 1000 ? 8 : value >= 100 ? 10 : 12;
                                ctx.font = `bold ${fontSize}px 'Segoe UI', Arial, sans-serif`;
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'middle';
                                ctx.fillStyle = getTextColor(value);
                                ctx.fillText(value.toString(), posX + tileSize/2, posY + tileSize/2);
                            } else {
                                // Puste pole
                                ctx.fillStyle = 'rgba(255,255,255,0.1)';
                                ctx.fillRect(posX, posY, tileSize, tileSize);
                            }
                        }
                    }

                    // Podświetl aktywną ścianę
                    if (face === activeFace) {
                        canvas.style.border = '2px solid #ffff00';
                        canvas.style.boxShadow = '0 0 10px #ffff00';
                    } else {
                        canvas.style.border = '2px solid #333';
                        canvas.style.boxShadow = 'none';
                    }
                });
            }

            function switchToFace(face) {
                if (faceColors[face]) {
                    activeFace = face;
                    document.getElementById('activeFaceDisplay').textContent = face.toUpperCase();
                    updateFacePreviews();

                    // Animacja obrotu kostki do pokazania aktywnej ściany
                    animateToFace(face);
                }
            }

            function animateToFace(face) {
                // Proste pozycjonowanie kamery dla różnych ścian
                const positions = {
                    front: { x: 8, y: 6, z: 8 },
                    back: { x: -8, y: 6, z: -8 },
                    left: { x: -8, y: 6, z: 8 },
                    right: { x: 8, y: 6, z: -8 },
                    top: { x: 0, y: 12, z: 0 },
                    bottom: { x: 0, y: -12, z: 0 }
                };

                const targetPos = positions[face] || positions.front;

                // Prosta animacja - można rozbudować
                const duration = 1000;
                const startPos = { x: camera.position.x, y: camera.position.y, z: camera.position.z };
                const startTime = Date.now();

                function animate() {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    const eased = 1 - Math.pow(1 - progress, 3); // easeOut

                    camera.position.x = startPos.x + (targetPos.x - startPos.x) * eased;
                    camera.position.y = startPos.y + (targetPos.y - startPos.y) * eased;
                    camera.position.z = startPos.z + (targetPos.z - startPos.z) * eased;
                    camera.lookAt(0, 0, 0);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                }
                animate();
            }

            // ===== FUNKCJE WARSTW RUBIKA =====

            function handleTileClick(event) {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

                raycaster.setFromCamera(mouse, camera);

                // Znajdź wszystkie kafelki
                const allTiles = [];
                Object.keys(tiles).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            allTiles.push({
                                mesh: tiles[face][x][y],
                                face: face,
                                x: x,
                                y: y
                            });
                        }
                    }
                });

                const intersects = raycaster.intersectObjects(allTiles.map(t => t.mesh));

                if (intersects.length > 0) {
                    const clickedTile = allTiles.find(t => t.mesh === intersects[0].object);
                    if (clickedTile) {
                        selectLayer(clickedTile);
                    }
                }
            }

            function selectLayer(tileInfo) {
                selectedLayer = {
                    face: tileInfo.face,
                    index: layerType === 'horizontal' ? tileInfo.y :
                           layerType === 'vertical' ? tileInfo.x :
                           tileInfo.face, // dla depth używamy całej ściany
                    type: layerType
                };

                updateLayerDisplay();
                highlightSelectedLayer();
            }

            function toggleLayerType() {
                const types = ['horizontal', 'vertical', 'depth'];
                const currentIndex = types.indexOf(layerType);
                layerType = types[(currentIndex + 1) % types.length];

                if (selectedLayer) {
                    // Przelicz zaznaczenie dla nowego typu
                    selectedLayer.type = layerType;
                    updateLayerDisplay();
                    highlightSelectedLayer();
                }
            }

            function updateLayerDisplay() {
                const layerInfo = document.getElementById('layerInfo');
                const layerDetails = document.getElementById('layerDetails');
                const layerControls = document.getElementById('layerControls');

                if (selectedLayer) {
                    layerInfo.style.display = 'block';
                    layerControls.style.display = 'block';

                    const typeNames = {
                        horizontal: 'Pozioma',
                        vertical: 'Pionowa',
                        depth: 'Głębokość'
                    };

                    layerDetails.innerHTML = `
                        <div>Ściana: <strong>${selectedLayer.face.toUpperCase()}</strong></div>
                        <div>Typ: <strong>${typeNames[selectedLayer.type]}</strong></div>
                        <div>Indeks: <strong>${selectedLayer.index}</strong></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #ccc;">
                            Prawy klik żeby zmienić typ warstwy
                        </div>
                    `;
                } else {
                    layerInfo.style.display = 'none';
                    layerControls.style.display = 'none';
                }
            }

            function highlightSelectedLayer() {
                // Reset wszystkich kafelków
                Object.keys(tiles).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            tiles[face][x][y].material.emissive.setHex(0x000000);
                        }
                    }
                });

                // Podświetl zaznaczoną warstwę
                if (selectedLayer) {
                    const { face, index, type } = selectedLayer;

                    if (type === 'horizontal') {
                        // Podświetl cały rząd
                        for (let x = 0; x < 3; x++) {
                            tiles[face][x][index].material.emissive.setHex(0x444400);
                        }
                    } else if (type === 'vertical') {
                        // Podświetl całą kolumnę
                        for (let y = 0; y < 3; y++) {
                            tiles[face][index][y].material.emissive.setHex(0x444400);
                        }
                    } else if (type === 'depth') {
                        // Podświetl całą ścianę
                        for (let x = 0; x < 3; x++) {
                            for (let y = 0; y < 3; y++) {
                                tiles[face][x][y].material.emissive.setHex(0x444400);
                            }
                        }
                    }
                }
            }

            function setupLayerControls() {
                document.getElementById('rotateLeft').addEventListener('click', () => {
                    if (selectedLayer) {
                        rotateLayer(selectedLayer, -1);
                    }
                });

                document.getElementById('rotateRight').addEventListener('click', () => {
                    if (selectedLayer) {
                        rotateLayer(selectedLayer, 1);
                    }
                });

                document.getElementById('clearSelection').addEventListener('click', () => {
                    selectedLayer = null;
                    updateLayerDisplay();
                    highlightSelectedLayer();
                });
            }

            function rotateLayer(layer, direction) {
                // Prosta implementacja obrotu warstwy
                const { face, index, type } = layer;

                if (type === 'horizontal') {
                    // Obróć rząd
                    const row = [];
                    for (let x = 0; x < 3; x++) {
                        row.push(gameData[face][x][index]);
                    }

                    if (direction === 1) {
                        // Prawo
                        const temp = row.pop();
                        row.unshift(temp);
                    } else {
                        // Lewo
                        const temp = row.shift();
                        row.push(temp);
                    }

                    for (let x = 0; x < 3; x++) {
                        gameData[face][x][index] = row[x];
                    }
                } else if (type === 'vertical') {
                    // Obróć kolumnę
                    const col = [];
                    for (let y = 0; y < 3; y++) {
                        col.push(gameData[face][index][y]);
                    }

                    if (direction === 1) {
                        // Dół
                        const temp = col.pop();
                        col.unshift(temp);
                    } else {
                        // Góra
                        const temp = col.shift();
                        col.push(temp);
                    }

                    for (let y = 0; y < 3; y++) {
                        gameData[face][index][y] = col[y];
                    }
                }

                updateTiles();
                highlightSelectedLayer();
            }
        }
    </script>
</body>
</html>
