<!DOCTYPE html>
<html lang="pl">
<head>
  <title>2048 3D Kostka Rubika</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #ui { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
          background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; max-width: 300px; }
    #preview { position: absolute; top: 10px; right: 10px; color: white; z-index: 100;
               background: rgba(0,0,0,0.9); padding: 20px; border-radius: 15px; }
    .face-row { display: flex; gap: 10px; margin-bottom: 10px; }
    .face-preview { text-align: center; cursor: pointer; padding: 8px; border-radius: 8px;
                    transition: all 0.3s ease; background: rgba(255,255,255,0.05); }
    .face-preview:hover { background: rgba(255,255,255,0.15); transform: scale(1.05); }
    .face-preview.active { background: rgba(255,255,0,0.3); border: 2px solid #ffff00; }
    .face-name { font-size: 10px; margin-bottom: 5px; font-weight: bold; }
    .face-preview canvas { border-radius: 6px; border: 1px solid rgba(255,255,255,0.2); }
    
    .cube-controls {
      margin-top: 20px;
      padding: 15px;
      background: rgba(0,0,0,0.8);
      border-radius: 8px;
    }
    
    .rotation-controls {
      margin: 10px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .rotation-controls label {
      min-width: 120px;
      color: white;
      font-size: 12px;
    }
    
    .rotation-controls input[type="range"] {
      flex: 1;
      height: 6px;
      background: #333;
      border-radius: 3px;
      outline: none;
    }
    
    .rotation-controls input[type="range"]::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: #4CAF50;
      border-radius: 50%;
      cursor: pointer;
    }
    
    .rotation-controls span {
      min-width: 40px;
      color: #4CAF50;
      font-size: 12px;
      text-align: right;
    }
    
    .cube-controls button {
      margin-top: 10px;
      padding: 8px 16px;
      background: #FF5722;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .cube-controls button:hover {
      background: #E64A19;
    }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="ui">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Wynik: <span id="score">0</span></div>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px; font-size: 11px;">
      <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
      <div><strong>🖱️ Mysz</strong> - obrót kostki (przeciągnij)</div>
      <div><strong>⌨️ Shift+Mysz</strong> - obrót wokół osi Z</div>
      <div><strong>🔄 Warstwy:</strong> Q/E (góra), W/S (środek), A/D (boki)</div>
    </div>
    
    <!-- Suwaki do obracania kostki -->
    <div class="cube-controls">
      <h4 style="color: white; margin: 0 0 10px 0;">🎛️ Sterowanie widokiem</h4>
      <div class="rotation-controls">
        <label>Obrót X (góra-dół):</label>
        <input type="range" id="rotationX" min="-360" max="360" value="0" step="1">
        <span id="rotationXValue">0°</span>
      </div>
      <div class="rotation-controls">
        <label>Obrót Y (lewo-prawo):</label>
        <input type="range" id="rotationY" min="-360" max="360" value="0" step="1">
        <span id="rotationYValue">0°</span>
      </div>
      <div class="rotation-controls">
        <label>Obrót Z (obrót płaszczyzny):</label>
        <input type="range" id="rotationZ" min="-360" max="360" value="0" step="1">
        <span id="rotationZValue">0°</span>
      </div>
      <button onclick="resetCubeRotation()">🔄 Reset widoku</button>
    </div>
  </div>
  
  <div id="preview">
    <h4>Podgląd ścian</h4>
    <div id="faces">
      <div class="face-row">
        <div class="face-preview" data-face="front">
          <div class="face-name">FRONT</div>
          <canvas id="canvas-front" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="back">
          <div class="face-name">BACK</div>
          <canvas id="canvas-back" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="left">
          <div class="face-name">LEFT</div>
          <canvas id="canvas-left" width="120" height="120"></canvas>
        </div>
      </div>
      <div class="face-row">
        <div class="face-preview" data-face="right">
          <div class="face-name">RIGHT</div>
          <canvas id="canvas-right" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="top">
          <div class="face-name">TOP</div>
          <canvas id="canvas-top" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="bottom">
          <div class="face-name">BOTTOM</div>
          <canvas id="canvas-bottom" width="120" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>

  <script>
    console.log('Inicjalizacja prawdziwej kostki Rubika 2048...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);

    // Dodatkowe światło dla podświetlenia aktywnej ściany
    const highlightLight = new THREE.DirectionalLight(0xffffff, 0.4);
    highlightLight.position.set(0, 0, 3); // Świeci z przodu na aktywną ścianę
    scene.add(highlightLight);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Pastelowe kolory ścian
    const faceColors = {
      front: '#ff9999',   // Pastelowy czerwony
      back: '#ffcc99',    // Pastelowy pomarańczowy
      left: '#99ccff',    // Pastelowy niebieski
      right: '#99ff99',   // Pastelowy zielony
      top: '#ffff99',     // Pastelowy żółty
      bottom: '#cc99ff'   // Pastelowy fioletowy
    };
    
    // Dane gry - każda ściana ma tablicę 3x3
    const gameData = {};
    const tileColors = {}; // Kolory kafelków (mogą się różnić od kolorów ścian po obrotach)
    
    Object.keys(faceColors).forEach(face => {
      gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
      tileColors[face] = [];
      for(let x = 0; x < 3; x++) {
        tileColors[face][x] = [];
        for(let y = 0; y < 3; y++) {
          tileColors[face][x][y] = faceColors[face];
        }
      }
    });
    
    // Dodaj początkowe cyfry na różnych ścianach
    gameData.front[0][0] = 2;
    gameData.front[2][2] = 4;
    gameData.top[1][1] = 8;
    gameData.right[0][1] = 2;
    gameData.left[2][0] = 16;
    
    let activeFace = 'front';
    let score = 0;
    const tiles = {};
    let isAnimating = false;
    
    // Kolory dla cyfr (jak w oryginalnym 2048)
    const numberColors = {
      2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
      32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
      512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
    };

    function getTextColor(value) {
      return [2, 4].includes(value) ? '#000000' : '#ffffff';
    }

    // Funkcje pomocnicze do kolorów
    function darkenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
      const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
      const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }

    function lightenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
      const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
      const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }

    // Tworzenie nowoczesnej tekstury dla kafelka z cyfrą
    function createTileTexture(value, faceColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 512;
      canvas.height = 512;
      const ctx = canvas.getContext('2d');

      // Gradient tła ściany - jaśniejszy na zewnątrz, ciemniejszy w środku
      const bgGradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
      bgGradient.addColorStop(0, darkenColor(faceColor, 0.1));
      bgGradient.addColorStop(1, faceColor);
      ctx.fillStyle = bgGradient;
      ctx.fillRect(0, 0, 512, 512);

      // Cienka ramka
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.15)';
      ctx.lineWidth = 2;
      ctx.strokeRect(1, 1, 510, 510);

      // Jeśli jest cyfra, narysuj nowoczesny kafelek z cyfrą
      if (value > 0) {
        const numberColor = numberColors[value] || '#f0f0f0';
        const textColor = getTextColor(value);

        // Bardziej zaokrąglony kafelek z większymi cyframi
        const margin = 25;
        const radius = 35;
        const tileSize = 512 - 2*margin;

        // Cień kafelka
        ctx.shadowColor = 'rgba(0, 0, 0, 0.25)';
        ctx.shadowBlur = 15;
        ctx.shadowOffsetX = 4;
        ctx.shadowOffsetY = 4;

        // Gradient kafelka - jaśniejszy na górze, ciemniejszy na dole
        const tileGradient = ctx.createLinearGradient(0, margin, 0, margin + tileSize);
        tileGradient.addColorStop(0, lightenColor(numberColor, 0.15));
        tileGradient.addColorStop(1, darkenColor(numberColor, 0.1));

        ctx.fillStyle = tileGradient;
        ctx.beginPath();
        ctx.roundRect(margin, margin, tileSize, tileSize, radius);
        ctx.fill();

        // Reset cienia
        ctx.shadowColor = 'transparent';

        // Delikatna ramka kafelka
        ctx.strokeStyle = darkenColor(numberColor, 0.15);
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(margin, margin, tileSize, tileSize, radius);
        ctx.stroke();

        // Cyfra - większa i wyraźniejsza
        const fontSize = value >= 1000 ? 100 : value >= 100 ? 120 : 140;
        ctx.font = `bold ${fontSize}px "Segoe UI", Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Cień cyfry
        ctx.fillStyle = textColor === '#ffffff' ? 'rgba(0,0,0,0.4)' : 'rgba(255,255,255,0.4)';
        ctx.fillText(value.toString(), 258, 258);

        // Główna cyfra
        ctx.fillStyle = textColor;
        ctx.fillText(value.toString(), 256, 256);
      }

      return new THREE.CanvasTexture(canvas);
    }

    // Tworzenie prawdziwej kostki Rubika
    function createRubiksCube() {
      console.log('Tworzenie kostki Rubika...');

      Object.keys(faceColors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y];
            const texture = createTileTexture(value, faceColor);

            const geometry = new THREE.PlaneGeometry(2.0, 2.0);
            const material = new THREE.MeshBasicMaterial({
              map: texture,
              transparent: true,
              opacity: 1.0
            });

            const tile = new THREE.Mesh(geometry, material);

            // Pozycjonowanie - dokładnie na ścianach kostki
            const posX = (x - 1) * 2.33;
            const posY = (1 - y) * 2.33; // Odwrócone Y
            const offset = 3.51;

            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, -posY);
                tile.rotation.x = Math.PI/2;
                break;
            }

            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });

      // Cienkie ramki kostki - tylko główne krawędzie
      const edgesGeometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(7.02, 7.02, 7.02));
      const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, opacity: 0.3, transparent: true });
      const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
      cubeGroup.add(cubeEdges);

      console.log('Kostka Rubika z ramkami utworzona!');
    }

    // Pozycja kamery
    camera.position.set(12, 8, 12);
    camera.lookAt(0, 0, 0);

    // Ustaw początkową pozycję kostki - front widoczny
    cubeGroup.rotation.x = 0;
    cubeGroup.rotation.y = 0;
    cubeGroup.rotation.z = 0;

    // Funkcje suwaków
    function updateSliders() {
      const rotXDeg = (cubeGroup.rotation.x * 180 / Math.PI).toFixed(0);
      const rotYDeg = (cubeGroup.rotation.y * 180 / Math.PI).toFixed(0);
      const rotZDeg = (cubeGroup.rotation.z * 180 / Math.PI).toFixed(0);

      document.getElementById('rotationX').value = rotXDeg;
      document.getElementById('rotationY').value = rotYDeg;
      document.getElementById('rotationZ').value = rotZDeg;
      document.getElementById('rotationXValue').textContent = rotXDeg + '°';
      document.getElementById('rotationYValue').textContent = rotYDeg + '°';
      document.getElementById('rotationZValue').textContent = rotZDeg + '°';
    }

    function resetCubeRotation() {
      cubeGroup.rotation.x = 0;
      cubeGroup.rotation.y = 0;
      cubeGroup.rotation.z = 0;
      activeFace = 'front';
      updateSliders();
      updateTiles();
      updatePreviews();
    }

    // Udostępnij funkcję globalnie
    window.resetCubeRotation = resetCubeRotation;

    // Obsługa suwaków - bez ograniczeń
    document.getElementById('rotationX').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.x = degrees * Math.PI / 180;
      document.getElementById('rotationXValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    document.getElementById('rotationY').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.y = degrees * Math.PI / 180;
      document.getElementById('rotationYValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    document.getElementById('rotationZ').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.z = degrees * Math.PI / 180;
      document.getElementById('rotationZValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę (obrót Z nie zmienia aktywnej ściany tak bardzo)
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    // Obsługa myszy
    let isDragging = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    renderer.domElement.addEventListener('mousedown', (event) => {
      isDragging = true;
      lastMouseX = event.clientX;
      lastMouseY = event.clientY;
    });

    renderer.domElement.addEventListener('mousemove', (event) => {
      if (!isDragging) return;

      const deltaX = event.clientX - lastMouseX;
      const deltaY = event.clientY - lastMouseY;

      // Sprawdź czy trzymany jest Shift dla obrotu Z
      if (event.shiftKey) {
        cubeGroup.rotation.z += deltaX * 0.01;
      } else {
        cubeGroup.rotation.y += deltaX * 0.01;
        cubeGroup.rotation.x += deltaY * 0.01;
      }

      lastMouseX = event.clientX;
      lastMouseY = event.clientY;

      updateSliders();

      // Wykryj aktywną ścianę podczas przeciągania
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    renderer.domElement.addEventListener('mouseup', () => {
      isDragging = false;
    });

    renderer.domElement.addEventListener('mouseleave', () => {
      isDragging = false;
    });

    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y];
            const texture = createTileTexture(value, faceColor);
            tiles[face][x][y].material.map = texture;
            tiles[face][x][y].material.needsUpdate = true;
          }
        }
      });

      // Aktualizuj UI
      document.getElementById('score').textContent = score;
      document.getElementById('active').textContent = activeFace.toUpperCase();

      // Aktualizuj podglądy
      if (typeof updatePreviews === 'function') {
        updatePreviews();
      }
    }

    // Mechanika 2048 - ruch w lewo
    function moveLeft() {
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        // Zbierz niepuste kafelki z wiersza
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z prawej strony
        while (row.length < 3) {
          row.push(0);
        }

        // Sprawdź czy coś się zmieniło
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][x][y] = row[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w prawo
    function moveRight() {
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        // Zbierz niepuste kafelki z wiersza (od prawej)
        for (let x = 2; x >= 0; x--) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z lewej strony
        while (row.length < 3) {
          row.push(0);
        }

        // Sprawdź czy coś się zmieniło i ustaw od prawej
        for (let x = 0; x < 3; x++) {
          if (gameData[face][2-x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][2-x][y] = row[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w górę
    function moveUp() {
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        // Zbierz niepuste kafelki z kolumny
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z dołu
        while (col.length < 3) {
          col.push(0);
        }

        // Sprawdź czy coś się zmieniło
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][y] = col[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w dół
    function moveDown() {
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        // Zbierz niepuste kafelki z kolumny (od dołu)
        for (let y = 2; y >= 0; y--) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z góry
        while (col.length < 3) {
          col.push(0);
        }

        // Sprawdź czy coś się zmieniło i ustaw od dołu
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][2-y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][2-y] = col[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Dodawanie losowego kafelka
    function addRandomTile() {
      const face = activeFace;
      const emptyTiles = [];

      // Znajdź puste miejsca na aktywnej ścianie
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] === 0) {
            emptyTiles.push({x, y});
          }
        }
      }

      if (emptyTiles.length > 0) {
        const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
        const value = Math.random() < 0.9 ? 2 : 4;
        gameData[face][randomTile.x][randomTile.y] = value;
        updateTiles();
      }
    }

    // Sterowanie klawiaturą
    document.addEventListener('keydown', (e) => {
      switch(e.code) {
        case 'ArrowLeft':
          e.preventDefault();
          moveLeft();
          break;
        case 'ArrowRight':
          e.preventDefault();
          moveRight();
          break;
        case 'ArrowUp':
          e.preventDefault();
          moveUp();
          break;
        case 'ArrowDown':
          e.preventDefault();
          moveDown();
          break;
      }
    });

    // Funkcja rysowania podglądu ściany
    function drawFacePreview(face) {
      const canvas = document.getElementById(`canvas-${face}`);
      const ctx = canvas.getContext('2d');

      // Wyczyść canvas
      ctx.clearRect(0, 0, 120, 120);

      // Tło ściany
      ctx.fillStyle = faceColors[face];
      ctx.fillRect(0, 0, 120, 120);

      // Narysuj siatkę 3x3
      const cellSize = 40;
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          const value = gameData[face][x][y];
          const startX = x * cellSize;
          const startY = y * cellSize;

          // Tło kafelka
          if (value > 0) {
            const numberColor = numberColors[value] || '#f0f0f0';
            ctx.fillStyle = numberColor;
          } else {
            ctx.fillStyle = darkenColor(faceColors[face], 0.1);
          }

          // Zaokrąglony kafelek
          ctx.beginPath();
          ctx.roundRect(startX + 2, startY + 2, cellSize - 4, cellSize - 4, 6);
          ctx.fill();

          // Cyfra
          if (value > 0) {
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = getTextColor(value);
            ctx.fillText(value.toString(), startX + cellSize/2, startY + cellSize/2);
          }
        }
      }

      // Ramka aktywnej ściany
      if (face === activeFace) {
        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 3;
        ctx.strokeRect(1, 1, 118, 118);
      }
    }

    // Aktualizacja wszystkich podglądów
    function updatePreviews() {
      Object.keys(faceColors).forEach(face => {
        drawFacePreview(face);
      });

      // Aktualizuj klasy CSS
      document.querySelectorAll('.face-preview').forEach(el => {
        el.classList.remove('active');
      });
      document.querySelector(`[data-face="${activeFace}"]`).classList.add('active');

      // Aktualizuj podświetlenie 3D kostki
      updateCubeHighlight(activeFace);
    }

    // Funkcja do podświetlenia aktywnej ściany na kostce 3D
    function updateCubeHighlight(activeFace) {
      // Resetuj wszystkie materiały do normalnego stanu
      Object.keys(faces).forEach(face => {
        faces[face].forEach(tile => {
          if (tile.material && tile.material.emissive) {
            tile.material.emissive.setHex(0x000000); // Usuń emisję
          }
        });
      });

      // Dodaj delikatne podświetlenie do aktywnej ściany
      if (faces[activeFace]) {
        faces[activeFace].forEach(tile => {
          if (tile.material && tile.material.emissive) {
            tile.material.emissive.setHex(0x222222); // Delikatna emisja
          }
        });
      }
    }

    // Pozycje kostki dla każdej ściany - wyprostowane i skierowane do użytkownika
    const faceRotations = {
      front: { x: -0.1, y: 0.1 },         // Lekko pochylone dla lepszego widoku
      back: { x: -0.1, y: Math.PI + 0.1 }, // Back z przodu + lekkie pochylenie
      left: { x: -0.1, y: Math.PI/2 + 0.1 }, // Left z przodu + pochylenie
      right: { x: -0.1, y: -Math.PI/2 + 0.1 }, // Right z przodu + pochylenie
      top: { x: Math.PI/2 - 0.2, y: 0.1 },     // Top z przodu, mniej pochylony
      bottom: { x: -Math.PI/3, y: 0.1 }  // Bottom z przodu, 60 stopni zamiast 90
    };

    // Animacja obrotu kostki
    function animateToFace(targetFace) {
      const target = faceRotations[targetFace];
      const startX = cubeGroup.rotation.x;
      const startY = cubeGroup.rotation.y;
      const duration = 800; // ms
      const startTime = Date.now();

      function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);

        cubeGroup.rotation.x = startX + (target.x - startX) * easeOut;
        cubeGroup.rotation.y = startY + (target.y - startY) * easeOut;

        updateSliders();

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      }

      animate();
    }

    // Wykrywanie aktywnej ściany na podstawie obrotu kostki
    function detectActiveFace() {
      const rotX = cubeGroup.rotation.x;
      const rotY = cubeGroup.rotation.y;

      // Normalizuj kąty do zakresu -π do π
      let normalizedX = ((rotX % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
      if (normalizedX > Math.PI) normalizedX -= 2 * Math.PI;

      let normalizedY = ((rotY % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
      if (normalizedY > Math.PI) normalizedY -= 2 * Math.PI;

      // Progi dla wykrywania ścian
      const threshold = Math.PI / 4; // 45 stopni

      // Sprawdź góra/dół (priorytet dla dużych obrotów X)
      if (normalizedX > threshold) {
        return 'top';    // Kostka obrócona w dół = top widoczny z przodu
      } else if (normalizedX < -threshold) {
        return 'bottom'; // Kostka obrócona w górę = bottom widoczny z przodu
      }

      // Sprawdź boki (dla małych obrotów X)
      if (normalizedY > threshold && normalizedY < Math.PI - threshold) {
        return 'left';   // Kostka obrócona w prawo = left widoczny z przodu
      } else if (normalizedY < -threshold && normalizedY > -Math.PI + threshold) {
        return 'right';  // Kostka obrócona w lewo = right widoczny z przodu
      } else if (Math.abs(normalizedY) > Math.PI - threshold) {
        return 'back';   // Kostka obrócona 180° = back widoczny z przodu
      } else {
        return 'front';  // Domyślnie front
      }
    }

    // Obsługa kliknięć na podglądy - obróć kostkę do wybranej ściany
    document.querySelectorAll('.face-preview').forEach(preview => {
      preview.addEventListener('click', () => {
        const face = preview.dataset.face;
        activeFace = face;
        animateToFace(face);
        updateTiles();
        updatePreviews();
      });
    });

    // Inicjalizacja
    createRubiksCube();
    updateTiles();
    updatePreviews();
    updateSliders();

    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    animate();

    console.log('Kostka gotowa!');
  </script>
</body>
</html>
