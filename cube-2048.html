<!DOCTYPE html>
<html>
<head>
    <title>2048 3D Kostka - FINAL</title>
    <style>
        body { margin: 0; background: #1a1a2e; color: white; font-family: Arial; overflow: hidden; }
        canvas { display: block; }
        #ui { position: absolute; top: 10px; left: 10px; z-index: 100; 
              background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    </style>
</head>
<body>
    <div id="ui">
        <h3>2048 3D Kostka Rubika</h3>
        <div>Wynik: <span id="score">0</span></div>
        <div>Status: <span id="status">Ładowanie...</span></div>
        <div style="margin-top: 10px; font-size: 11px;">
            <div><strong>🎮 Strzałki</strong> - gra 2048</div>
            <div><strong>🖱️ Mysz</strong> - obrót kostki</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        const status = document.getElementById('status');
        
        function updateStatus(msg) {
            console.log(msg);
            status.innerHTML = msg;
        }
        
        updateStatus('Uruchamianie...');
        
        if (typeof THREE === 'undefined') {
            updateStatus('BŁĄD: THREE.js nie załadowane!');
        } else {
            updateStatus('THREE.js OK');
            
            // Zmienne globalne
            let scene, camera, renderer, cubeGroup;
            let gameData = {};
            let tiles = {};
            let activeFace = 'front';
            let score = 0;
            
            // Kolory ścian
            const faceColors = {
                front: '#ff9999',   back: '#ffcc99',   left: '#99ccff',
                right: '#99ff99',   top: '#ffff99',    bottom: '#cc99ff'
            };
            
            // Kolory cyfr
            const numberColors = {
                2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
                32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
                512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
            };
            
            function getTextColor(value) {
                return [2, 4].includes(value) ? '#000000' : '#ffffff';
            }
            
            function createTileTexture(value, faceColor) {
                const canvas = document.createElement('canvas');
                canvas.width = 256;
                canvas.height = 256;
                const ctx = canvas.getContext('2d');

                // Tło ściany
                ctx.fillStyle = faceColor;
                ctx.fillRect(0, 0, 256, 256);

                // Ramka
                ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
                ctx.lineWidth = 2;
                ctx.strokeRect(1, 1, 254, 254);

                // Jeśli jest cyfra
                if (value > 0) {
                    const numberColor = numberColors[value] || '#f0f0f0';
                    const textColor = getTextColor(value);

                    // Kafelek
                    const margin = 15;
                    const tileSize = 256 - 2*margin;

                    ctx.fillStyle = numberColor;
                    ctx.fillRect(margin, margin, tileSize, tileSize);

                    // Cyfra
                    const fontSize = value >= 1000 ? 50 : value >= 100 ? 60 : 70;
                    ctx.font = `bold ${fontSize}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = textColor;
                    ctx.fillText(value.toString(), 128, 128);
                }

                return new THREE.CanvasTexture(canvas);
            }
            
            try {
                updateStatus('Tworzenie sceny...');
                
                // Scena
                scene = new THREE.Scene();
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                renderer = new THREE.WebGLRenderer({ antialias: true });
                
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x1a1a2e);
                document.body.appendChild(renderer.domElement);
                
                updateStatus('Dodawanie światła...');
                
                // Światło
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                scene.add(directionalLight);
                
                // Grupa kostki
                cubeGroup = new THREE.Group();
                scene.add(cubeGroup);
                
                updateStatus('Inicjalizacja danych...');
                
                // Inicjalizuj dane gry
                Object.keys(faceColors).forEach(face => {
                    gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
                    tiles[face] = [];
                });
                
                // Dodaj początkowe cyfry
                gameData.front[0][0] = 2;
                gameData.front[2][2] = 4;
                gameData.top[1][1] = 8;
                
                updateStatus('Tworzenie kostki...');
                
                // Stwórz kostkę
                Object.keys(faceColors).forEach(face => {
                    tiles[face] = [];
                    for (let x = 0; x < 3; x++) {
                        tiles[face][x] = [];
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const texture = createTileTexture(value, faceColors[face]);

                            const geometry = new THREE.PlaneGeometry(1.5, 1.5);
                            const material = new THREE.MeshLambertMaterial({
                                map: texture,
                                transparent: true
                            });

                            const tile = new THREE.Mesh(geometry, material);

                            // Pozycjonowanie
                            const posX = (x - 1) * 1.8;
                            const posY = (1 - y) * 1.8;
                            const offset = 2.7;

                            switch(face) {
                                case 'front':
                                    tile.position.set(posX, posY, offset);
                                    break;
                                case 'back':
                                    tile.position.set(-posX, posY, -offset);
                                    tile.rotation.y = Math.PI;
                                    break;
                                case 'right':
                                    tile.position.set(offset, posY, -posX);
                                    tile.rotation.y = Math.PI/2;
                                    break;
                                case 'left':
                                    tile.position.set(-offset, posY, posX);
                                    tile.rotation.y = -Math.PI/2;
                                    break;
                                case 'top':
                                    tile.position.set(posX, offset, posY);
                                    tile.rotation.x = -Math.PI/2;
                                    break;
                                case 'bottom':
                                    tile.position.set(posX, -offset, -posY);
                                    tile.rotation.x = Math.PI/2;
                                    break;
                            }

                            tiles[face][x][y] = tile;
                            cubeGroup.add(tile);
                        }
                    }
                });
                
                // Ramki kostki
                const boxGeometry = new THREE.BoxGeometry(7, 7, 7);
                const edgesGeometry = new THREE.EdgesGeometry(boxGeometry);
                const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, opacity: 0.3, transparent: true });
                const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
                cubeGroup.add(cubeEdges);
                
                updateStatus('Ustawianie kamery...');
                
                // Pozycja kamery
                camera.position.set(8, 6, 8);
                camera.lookAt(0, 0, 0);
                
                updateStatus('Dodawanie kontrolek...');
                
                // Obsługa myszy
                let isDragging = false;
                let lastMouseX = 0, lastMouseY = 0;
                
                renderer.domElement.addEventListener('mousedown', (event) => {
                    isDragging = true;
                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });
                
                renderer.domElement.addEventListener('mousemove', (event) => {
                    if (!isDragging) return;
                    
                    const deltaX = event.clientX - lastMouseX;
                    const deltaY = event.clientY - lastMouseY;
                    
                    cubeGroup.rotation.y += deltaX * 0.01;
                    cubeGroup.rotation.x += deltaY * 0.01;
                    
                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });
                
                renderer.domElement.addEventListener('mouseup', () => {
                    isDragging = false;
                });
                
                updateStatus('Uruchamianie animacji...');
                
                // Animacja
                function animate() {
                    requestAnimationFrame(animate);
                    renderer.render(scene, camera);
                }
                animate();
                
                updateStatus('GOTOWE - kostka powinna być widoczna!');
                
            } catch (error) {
                updateStatus('BŁĄD: ' + error.message);
                console.error('Błąd inicjalizacji:', error);
            }
        }
    </script>
</body>
</html>
