<!DOCTYPE html>
<html>
<head>
    <title>2048 3D Kostka Rubika</title>
    <style>
        body { margin: 0; background: #1a1a2e; color: white; font-family: Arial; }
        canvas { display: block; }
        #ui { position: absolute; top: 10px; left: 10px; z-index: 100; 
              background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    </style>
</head>
<body>
    <div id="ui">
        <h3>2048 3D Kostka Rubika</h3>
        <div>Wynik: <span id="score">0</span></div>
        <div>Aktywna ściana: <span id="active">FRONT</span></div>
        <div style="margin-top: 10px; font-size: 11px;">
            <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
            <div><strong>🖱️ Mysz</strong> - obr<PERSON>t kost<PERSON> (przeciągnij)</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        console.log('=== 2048 3D KOSTKA RUBIKA ===');
        
        if (typeof THREE === 'undefined') {
            console.error('THREE.js nie załadowane!');
            alert('Błąd: THREE.js nie załadowane!');
        } else {
            console.log('THREE.js OK, wersja:', THREE.REVISION);
            initGame();
        }
        
        // Zmienne globalne
        let scene, camera, renderer, cubeGroup;
        let gameData = {};
        let tiles = {};
        let activeFace = 'front';
        let score = 0;
        
        // Kolory ścian
        const faceColors = {
            front: '#ff9999',   back: '#ffcc99',   left: '#99ccff',
            right: '#99ff99',   top: '#ffff99',    bottom: '#cc99ff'
        };
        
        // Kolory cyfr (jak w 2048)
        const numberColors = {
            2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
            32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
            512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
        };
        
        function getTextColor(value) {
            return [2, 4].includes(value) ? '#000000' : '#ffffff';
        }
        
        function createTileTexture(value, faceColor) {
            const canvas = document.createElement('canvas');
            canvas.width = 512;
            canvas.height = 512;
            const ctx = canvas.getContext('2d');

            // Tło ściany
            ctx.fillStyle = faceColor;
            ctx.fillRect(0, 0, 512, 512);

            // Ramka
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.15)';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, 510, 510);

            // Jeśli jest cyfra, narysuj kafelek
            if (value > 0) {
                const numberColor = numberColors[value] || '#f0f0f0';
                const textColor = getTextColor(value);

                // Kafelek
                const margin = 25;
                const radius = 35;
                const tileSize = 512 - 2*margin;

                ctx.fillStyle = numberColor;
                ctx.beginPath();
                ctx.roundRect(margin, margin, tileSize, tileSize, radius);
                ctx.fill();

                // Cyfra
                const fontSize = value >= 1000 ? 100 : value >= 100 ? 120 : 140;
                ctx.font = `bold ${fontSize}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = textColor;
                ctx.fillText(value.toString(), 256, 256);
            }

            return new THREE.CanvasTexture(canvas);
        }
        
        function createCube() {
            console.log('Tworzenie kostki...');
            
            Object.keys(faceColors).forEach(face => {
                tiles[face] = [];
                for (let x = 0; x < 3; x++) {
                    tiles[face][x] = [];
                    for (let y = 0; y < 3; y++) {
                        const value = gameData[face][x][y];
                        const texture = createTileTexture(value, faceColors[face]);

                        const geometry = new THREE.PlaneGeometry(1.5, 1.5);
                        const material = new THREE.MeshLambertMaterial({
                            map: texture,
                            transparent: true,
                            opacity: 1.0
                        });

                        const tile = new THREE.Mesh(geometry, material);

                        // Pozycjonowanie
                        const posX = (x - 1) * 1.8;
                        const posY = (1 - y) * 1.8;
                        const offset = 2.7;

                        switch(face) {
                            case 'front':
                                tile.position.set(posX, posY, offset);
                                break;
                            case 'back':
                                tile.position.set(-posX, posY, -offset);
                                tile.rotation.y = Math.PI;
                                break;
                            case 'right':
                                tile.position.set(offset, posY, -posX);
                                tile.rotation.y = Math.PI/2;
                                break;
                            case 'left':
                                tile.position.set(-offset, posY, posX);
                                tile.rotation.y = -Math.PI/2;
                                break;
                            case 'top':
                                tile.position.set(posX, offset, posY);
                                tile.rotation.x = -Math.PI/2;
                                break;
                            case 'bottom':
                                tile.position.set(posX, -offset, -posY);
                                tile.rotation.x = Math.PI/2;
                                break;
                        }

                        tiles[face][x][y] = tile;
                        cubeGroup.add(tile);
                    }
                }
            });
            
            // Ramki kostki
            const boxGeometry = new THREE.BoxGeometry(7.02, 7.02, 7.02);
            const edgesGeometry = new THREE.EdgesGeometry(boxGeometry);
            const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, opacity: 0.3, transparent: true });
            const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
            cubeGroup.add(cubeEdges);
            
            console.log('Kostka utworzona!');
        }
        
        function initGame() {
            console.log('Inicjalizacja gry...');
            
            // Scena
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer();
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x1a1a2e);
            document.body.appendChild(renderer.domElement);
            
            // Grupa kostki
            cubeGroup = new THREE.Group();
            scene.add(cubeGroup);
            
            // Światło
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Inicjalizuj dane gry
            Object.keys(faceColors).forEach(face => {
                gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
            });
            
            // Dodaj początkowe cyfry
            gameData.front[0][0] = 2;
            gameData.front[2][2] = 4;
            gameData.top[1][1] = 8;
            
            // Stwórz kostkę
            createCube();
            
            // Pozycja kamery
            camera.position.set(8, 6, 8);
            camera.lookAt(0, 0, 0);
            
            // Obsługa myszy
            setupMouseControls();
            
            // Animacja
            animate();
            
            console.log('Gra zainicjalizowana!');
        }
        
        function setupMouseControls() {
            let isDragging = false;
            let lastMouseX = 0, lastMouseY = 0;
            
            renderer.domElement.addEventListener('mousedown', (event) => {
                isDragging = true;
                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            });
            
            renderer.domElement.addEventListener('mousemove', (event) => {
                if (!isDragging) return;
                
                const deltaX = event.clientX - lastMouseX;
                const deltaY = event.clientY - lastMouseY;
                
                cubeGroup.rotation.y += deltaX * 0.01;
                cubeGroup.rotation.x += deltaY * 0.01;
                
                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            });
            
            renderer.domElement.addEventListener('mouseup', () => {
                isDragging = false;
            });
        }
        
        function updateTiles() {
            Object.keys(gameData).forEach(face => {
                for (let x = 0; x < 3; x++) {
                    for (let y = 0; y < 3; y++) {
                        const value = gameData[face][x][y];
                        const texture = createTileTexture(value, faceColors[face]);
                        tiles[face][x][y].material.map = texture;
                        tiles[face][x][y].material.needsUpdate = true;
                    }
                }
            });

            // Aktualizuj UI
            document.getElementById('score').textContent = score;
            document.getElementById('active').textContent = activeFace.toUpperCase();
        }

        // Mechanika 2048 - ruch w lewo
        function moveLeft() {
            let moved = false;
            const face = activeFace;

            for (let y = 0; y < 3; y++) {
                const row = [];
                // Zbierz niepuste kafelki z wiersza
                for (let x = 0; x < 3; x++) {
                    if (gameData[face][x][y] > 0) {
                        row.push(gameData[face][x][y]);
                    }
                }

                // Połącz sąsiadujące kafelki o tej samej wartości
                for (let i = 0; i < row.length - 1; i++) {
                    if (row[i] === row[i + 1]) {
                        row[i] *= 2;
                        score += row[i];
                        row.splice(i + 1, 1);
                    }
                }

                // Wypełnij wiersz zerami z prawej strony
                while (row.length < 3) {
                    row.push(0);
                }

                // Sprawdź czy coś się zmieniło
                for (let x = 0; x < 3; x++) {
                    if (gameData[face][x][y] !== row[x]) {
                        moved = true;
                    }
                    gameData[face][x][y] = row[x];
                }
            }

            if (moved) {
                updateTiles();
                setTimeout(addRandomTile, 200);
            }
        }

        // Mechanika 2048 - ruch w prawo
        function moveRight() {
            let moved = false;
            const face = activeFace;

            for (let y = 0; y < 3; y++) {
                const row = [];
                // Zbierz niepuste kafelki z wiersza (od prawej)
                for (let x = 2; x >= 0; x--) {
                    if (gameData[face][x][y] > 0) {
                        row.push(gameData[face][x][y]);
                    }
                }

                // Połącz sąsiadujące kafelki o tej samej wartości
                for (let i = 0; i < row.length - 1; i++) {
                    if (row[i] === row[i + 1]) {
                        row[i] *= 2;
                        score += row[i];
                        row.splice(i + 1, 1);
                    }
                }

                // Wypełnij wiersz zerami z lewej strony
                while (row.length < 3) {
                    row.push(0);
                }

                // Sprawdź czy coś się zmieniło i ustaw od prawej
                for (let x = 0; x < 3; x++) {
                    if (gameData[face][2-x][y] !== row[x]) {
                        moved = true;
                    }
                    gameData[face][2-x][y] = row[x];
                }
            }

            if (moved) {
                updateTiles();
                setTimeout(addRandomTile, 200);
            }
        }

        // Mechanika 2048 - ruch w górę
        function moveUp() {
            let moved = false;
            const face = activeFace;

            for (let x = 0; x < 3; x++) {
                const col = [];
                // Zbierz niepuste kafelki z kolumny
                for (let y = 0; y < 3; y++) {
                    if (gameData[face][x][y] > 0) {
                        col.push(gameData[face][x][y]);
                    }
                }

                // Połącz sąsiadujące kafelki o tej samej wartości
                for (let i = 0; i < col.length - 1; i++) {
                    if (col[i] === col[i + 1]) {
                        col[i] *= 2;
                        score += col[i];
                        col.splice(i + 1, 1);
                    }
                }

                // Wypełnij kolumnę zerami z dołu
                while (col.length < 3) {
                    col.push(0);
                }

                // Sprawdź czy coś się zmieniło
                for (let y = 0; y < 3; y++) {
                    if (gameData[face][x][y] !== col[y]) {
                        moved = true;
                    }
                    gameData[face][x][y] = col[y];
                }
            }

            if (moved) {
                updateTiles();
                setTimeout(addRandomTile, 200);
            }
        }

        // Mechanika 2048 - ruch w dół
        function moveDown() {
            let moved = false;
            const face = activeFace;

            for (let x = 0; x < 3; x++) {
                const col = [];
                // Zbierz niepuste kafelki z kolumny (od dołu)
                for (let y = 2; y >= 0; y--) {
                    if (gameData[face][x][y] > 0) {
                        col.push(gameData[face][x][y]);
                    }
                }

                // Połącz sąsiadujące kafelki o tej samej wartości
                for (let i = 0; i < col.length - 1; i++) {
                    if (col[i] === col[i + 1]) {
                        col[i] *= 2;
                        score += col[i];
                        col.splice(i + 1, 1);
                    }
                }

                // Wypełnij kolumnę zerami z góry
                while (col.length < 3) {
                    col.push(0);
                }

                // Sprawdź czy coś się zmieniło i ustaw od dołu
                for (let y = 0; y < 3; y++) {
                    if (gameData[face][x][2-y] !== col[y]) {
                        moved = true;
                    }
                    gameData[face][x][2-y] = col[y];
                }
            }

            if (moved) {
                updateTiles();
                setTimeout(addRandomTile, 200);
            }
        }

        // Dodawanie losowego kafelka
        function addRandomTile() {
            const emptyTiles = [];
            const face = activeFace;

            for (let x = 0; x < 3; x++) {
                for (let y = 0; y < 3; y++) {
                    if (gameData[face][x][y] === 0) {
                        emptyTiles.push({x, y});
                    }
                }
            }

            if (emptyTiles.length > 0) {
                const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
                // 90% szans na 2, 10% szans na 4
                const value = Math.random() < 0.9 ? 2 : 4;
                gameData[face][randomTile.x][randomTile.y] = value;
                updateTiles();
            }
        }

        // Obsługa klawiatury
        document.addEventListener('keydown', (event) => {
            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    moveLeft();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    moveRight();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    moveUp();
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    moveDown();
                    break;
            }
        });

        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
