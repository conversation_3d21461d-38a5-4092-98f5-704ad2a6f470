<!DOCTYPE html>
<html lang="pl">
<head>
  <title>2048 3D Kostka Rubika</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    canvas { display: block; position: fixed; top: 0; left: 0; z-index: 1; width: 100vw; height: 100vh; }
    #ui { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
          background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; max-width: 300px; }
    #preview { position: absolute; top: 10px; right: 10px; color: white; z-index: 100;
               background: rgba(0,0,0,0.9); padding: 20px; border-radius: 15px; }
    .face-row { display: flex; gap: 10px; margin-bottom: 10px; }
    .face-preview { text-align: center; cursor: pointer; padding: 8px; border-radius: 8px;
                    transition: all 0.3s ease; background: rgba(255,255,255,0.05); }
    .face-preview:hover { background: rgba(255,255,255,0.15); transform: scale(1.05); }
    .face-preview.active { background: rgba(255,255,0,0.3); border: 2px solid #ffff00; }
    .face-name { font-size: 10px; margin-bottom: 5px; font-weight: bold; }
    .face-preview canvas { border-radius: 6px; border: 1px solid rgba(255,255,255,0.2); }
    
    .cube-controls, .layer-controls {
      margin-top: 20px;
      padding: 15px;
      background: rgba(0,0,0,0.8);
      border-radius: 8px;
    }

    .layer-row {
      margin: 10px 0;
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      align-items: center;
    }

    .layer-row label {
      min-width: 120px;
      font-size: 12px;
      margin-right: 10px;
    }

    .layer-row button {
      padding: 5px 8px;
      font-size: 10px;
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.2s;
    }

    .layer-row button:hover {
      background: rgba(255,255,255,0.2);
    }

    .layer-controls {
      display: none; /* Ukryj domyślnie */
    }

    .layer-controls.show {
      display: block;
    }

    .selected-layer-info {
      background: rgba(255,255,0,0.2);
      border: 2px solid #ffff00;
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 10px;
      color: white;
      font-weight: bold;
    }
    
    .rotation-controls {
      margin: 10px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .rotation-controls label {
      min-width: 120px;
      color: white;
      font-size: 12px;
    }
    
    .rotation-controls input[type="range"] {
      flex: 1;
      height: 6px;
      background: #333;
      border-radius: 3px;
      outline: none;
    }
    
    .rotation-controls input[type="range"]::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: #4CAF50;
      border-radius: 50%;
      cursor: pointer;
    }
    
    .rotation-controls span {
      min-width: 40px;
      color: #4CAF50;
      font-size: 12px;
      text-align: right;
    }
    
    .cube-controls button {
      margin-top: 10px;
      padding: 8px 16px;
      background: #FF5722;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .cube-controls button:hover {
      background: #E64A19;
    }

    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js" onload="console.log('Three.js załadowane!')" onerror="console.error('Błąd ładowania Three.js!')"></script>
</head>
<body>
  <div id="ui">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Wynik: <span id="score">0</span></div>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px;">
      <label style="color: white; font-size: 13px;">
        <input type="checkbox" id="anyFaceSpawn" checked>
        Nowe kafelki na dowolnej ścianie
      </label>
      <div style="color: #ccc; font-size: 11px; margin-top: 3px;">
        Prawdopodobieństwo: 90% → 2, 10% → 4
      </div>
    </div>
    <div style="margin-top: 10px; font-size: 11px;">
      <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
      <div><strong>🖱️ Mysz</strong> - obrót kostki (przeciągnij)</div>
      <div><strong>⌨️ Shift+Mysz</strong> - obrót wokół osi Z</div>
      <div><strong>🔄 Warstwy Rubika:</strong></div>
      <div style="margin-left: 10px; font-size: 10px;">
        <div>• <strong>Lewy klik</strong> na kostce - zaznacz warstwę</div>
        <div>• <strong>Prawy klik</strong> na kostce - przełącz typ warstwy</div>
        <div>• <strong>Klik na strzałki</strong> - obróć zaznaczoną warstwę</div>
        <div>• Środkowe kostki należą do 2-3 warstw jednocześnie</div>
      </div>
    </div>
    
    <!-- Suwaki do obracania kostki -->
    <div class="cube-controls">
      <h4 style="color: white; margin: 0 0 10px 0;">🎛️ Sterowanie widokiem</h4>
      <div class="rotation-controls">
        <label>Obrót X (góra-dół):</label>
        <input type="range" id="rotationX" min="-360" max="360" value="0" step="1">
        <span id="rotationXValue">0°</span>
      </div>
      <div class="rotation-controls">
        <label>Obrót Y (lewo-prawo):</label>
        <input type="range" id="rotationY" min="-360" max="360" value="0" step="1">
        <span id="rotationYValue">0°</span>
      </div>
      <div class="rotation-controls">
        <label>Obrót Z (obrót płaszczyzny):</label>
        <input type="range" id="rotationZ" min="-360" max="360" value="0" step="1">
        <span id="rotationZValue">0°</span>
      </div>
      <button onclick="resetCubeRotation()">🔄 Reset widoku</button>
    </div>

    <div class="layer-controls" id="layerControls" style="display: none;">
      <div class="selected-layer-info" id="selectedLayerInfo" style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 5px; margin-bottom: 10px;">
        Kliknij na kostkę żeby zaznaczyć warstwę Rubika
      </div>
      <div style="margin-top: 10px;">
        <button onclick="rotateSelectedLayer(1)" style="background-color: #4CAF50; color: white; border: none; padding: 8px 12px; margin: 2px; border-radius: 4px; cursor: pointer;">Obróć w prawo ↻</button>
        <button onclick="rotateSelectedLayer(-1)" style="background-color: #2196F3; color: white; border: none; padding: 8px 12px; margin: 2px; border-radius: 4px; cursor: pointer;">Obróć w lewo ↺</button>
        <button onclick="clearLayerSelection()" style="background-color: #ff6b6b; color: white; border: none; padding: 8px 12px; margin: 2px; border-radius: 4px; cursor: pointer;">Odznacz warstwę ✕</button>
      </div>
    </div>
  </div>
  
  <div id="preview">
    <h4>Podgląd ścian</h4>
    <div id="faces">
      <div class="face-row">
        <div class="face-preview" data-face="front">
          <div class="face-name">FRONT</div>
          <canvas id="canvas-front" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="back">
          <div class="face-name">BACK</div>
          <canvas id="canvas-back" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="left">
          <div class="face-name">LEFT</div>
          <canvas id="canvas-left" width="120" height="120"></canvas>
        </div>
      </div>
      <div class="face-row">
        <div class="face-preview" data-face="right">
          <div class="face-name">RIGHT</div>
          <canvas id="canvas-right" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="top">
          <div class="face-name">TOP</div>
          <canvas id="canvas-top" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="bottom">
          <div class="face-name">BOTTOM</div>
          <canvas id="canvas-bottom" width="120" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>

  <script>
    console.log('Inicjalizacja prawdziwej kostki Rubika 2048...');

    // Test czy Three.js jest załadowane
    if (typeof THREE === 'undefined') {
      console.error('THREE.js nie jest załadowane!');
      alert('Błąd: Three.js nie jest załadowane!');
    } else {
      console.log('Three.js wersja:', THREE.REVISION);
      console.log('Dostępne klasy THREE:', Object.keys(THREE).slice(0, 10));

      // Kontynuuj tylko jeśli Three.js jest załadowane
      initializeEverything();
    }

    // Zmienne globalne dla Three.js
    let scene, camera, renderer, cubeGroup;

    function initializeEverything() {
      console.log('=== INICJALIZACJA WSZYSTKIEGO ===');

      // Scena
      scene = new THREE.Scene();
      camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      // Sprawdź obsługę WebGL
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        console.error('WebGL nie jest obsługiwane!');
        alert('Twoja przeglądarka nie obsługuje WebGL!');
        return; // teraz to jest w funkcji, więc return jest OK
      }
      console.log('WebGL jest obsługiwane');

      renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: false,
        preserveDrawingBuffer: true
      });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x1a1a2e, 1.0); // Ciemne tło
      renderer.setPixelRatio(window.devicePixelRatio);

      // Dodaj canvas na początek body
      document.body.insertBefore(renderer.domElement, document.body.firstChild);

      console.log('Renderer utworzony:', renderer);
      console.log('Canvas dodany do DOM:', document.body.contains(renderer.domElement));
      console.log('Rozmiar canvas:', renderer.domElement.width, 'x', renderer.domElement.height);
      console.log('WebGL context:', renderer.getContext());

      // Grupa kostki
      cubeGroup = new THREE.Group();
      scene.add(cubeGroup);

    // Światło dla MeshLambertMaterial
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);


    // Dodatkowe światło dla podświetlenia aktywnej ściany
    const highlightLight = new THREE.DirectionalLight(0xffffff, 0.4);
    highlightLight.position.set(0, 0, 3); // Świeci z przodu na aktywną ścianę
    scene.add(highlightLight);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Pastelowe kolory ścian
    const faceColors = {
      front: '#ff9999',   // Pastelowy czerwony
      back: '#ffcc99',    // Pastelowy pomarańczowy
      left: '#99ccff',    // Pastelowy niebieski
      right: '#99ff99',   // Pastelowy zielony
      top: '#ffff99',     // Pastelowy żółty
      bottom: '#cc99ff'   // Pastelowy fioletowy
    };
    
    // Dane gry - każda ściana ma tablicę 3x3
    const gameData = {};
    const tileColors = {}; // Kolory kafelków (mogą się różnić od kolorów ścian po obrotach)
    
    Object.keys(faceColors).forEach(face => {
      gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
      tileColors[face] = [];
      for(let x = 0; x < 3; x++) {
        tileColors[face][x] = [];
        for(let y = 0; y < 3; y++) {
          tileColors[face][x][y] = faceColors[face];
        }
      }
    });
    
    // Dodaj początkowe cyfry na różnych ścianach
    gameData.front[0][0] = 2;
    gameData.front[2][2] = 4;
    gameData.top[1][1] = 8;
    gameData.right[0][1] = 2;
    gameData.left[2][0] = 16;
    
    let activeFace = 'bottom';  // Zacznij od fioletowej ściany
    let score = 0;
    const tiles = {};
    let isAnimating = false;

    // Zmienne do zaznaczania warstw
    let selectedLayer = null; // {axis: 'horizontal', index: 0}
    let highlightedCubes = [];
    let directionArrows = []; // Strzałki kierunków obrotu
    
    // Kolory dla cyfr (jak w oryginalnym 2048)
    const numberColors = {
      2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
      32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
      512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
    };

    function getTextColor(value) {
      return [2, 4].includes(value) ? '#000000' : '#ffffff';
    }

    // Funkcje pomocnicze do kolorów
    function darkenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
      const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
      const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }

    function lightenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
      const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
      const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }

    // Tworzenie nowoczesnej tekstury dla kafelka z cyfrą
    function createTileTexture(value, faceColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 512;
      canvas.height = 512;
      const ctx = canvas.getContext('2d');

      // Gradient tła ściany - jaśniejszy na zewnątrz, ciemniejszy w środku
      const bgGradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
      bgGradient.addColorStop(0, darkenColor(faceColor, 0.1));
      bgGradient.addColorStop(1, faceColor);
      ctx.fillStyle = bgGradient;
      ctx.fillRect(0, 0, 512, 512);

      // Cienka ramka
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.15)';
      ctx.lineWidth = 2;
      ctx.strokeRect(1, 1, 510, 510);

      // Jeśli jest cyfra, narysuj nowoczesny kafelek z cyfrą
      if (value > 0) {
        const numberColor = numberColors[value] || '#f0f0f0';
        const textColor = getTextColor(value);

        // Bardziej zaokrąglony kafelek z większymi cyframi
        const margin = 25;
        const radius = 35;
        const tileSize = 512 - 2*margin;

        // Cień kafelka
        ctx.shadowColor = 'rgba(0, 0, 0, 0.25)';
        ctx.shadowBlur = 15;
        ctx.shadowOffsetX = 4;
        ctx.shadowOffsetY = 4;

        // Gradient kafelka - jaśniejszy na górze, ciemniejszy na dole
        const tileGradient = ctx.createLinearGradient(0, margin, 0, margin + tileSize);
        tileGradient.addColorStop(0, lightenColor(numberColor, 0.15));
        tileGradient.addColorStop(1, darkenColor(numberColor, 0.1));

        ctx.fillStyle = tileGradient;
        ctx.beginPath();
        ctx.roundRect(margin, margin, tileSize, tileSize, radius);
        ctx.fill();

        // Reset cienia
        ctx.shadowColor = 'transparent';

        // Delikatna ramka kafelka
        ctx.strokeStyle = darkenColor(numberColor, 0.15);
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(margin, margin, tileSize, tileSize, radius);
        ctx.stroke();

        // Cyfra - większa i wyraźniejsza
        const fontSize = value >= 1000 ? 100 : value >= 100 ? 120 : 140;
        ctx.font = `bold ${fontSize}px "Segoe UI", Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Cień cyfry
        ctx.fillStyle = textColor === '#ffffff' ? 'rgba(0,0,0,0.4)' : 'rgba(255,255,255,0.4)';
        ctx.fillText(value.toString(), 258, 258);

        // Główna cyfra
        ctx.fillStyle = textColor;
        ctx.fillText(value.toString(), 256, 256);
      }

      return new THREE.CanvasTexture(canvas);
    }

    // Tworzenie prawdziwej kostki Rubika
    function createRubiksCube() {
      console.log('Tworzenie kostki Rubika...');
      console.log('Scene exists:', !!scene);
      console.log('CubeGroup exists:', !!cubeGroup);
      console.log('Scene:', scene);
      console.log('CubeGroup:', cubeGroup);

      // Test czy wszystkie zmienne są zdefiniowane
      console.log('faceColors:', faceColors);
      console.log('gameData:', gameData);
      console.log('tileColors:', tileColors);

      let tilesCreated = 0;

      Object.keys(faceColors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            try {
              const value = gameData[face][x][y];
              const faceColor = tileColors[face][x][y];
              const texture = createTileTexture(value, faceColor);

              const geometry = new THREE.PlaneGeometry(1.5, 1.5);
              const material = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                opacity: 1.0
              });

              const tile = new THREE.Mesh(geometry, material);

            // Pozycjonowanie - dokładnie na ścianach kostki (zmniejszone)
            const posX = (x - 1) * 1.8;
            const posY = (1 - y) * 1.8; // Odwrócone Y
            const offset = 2.7;

            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, -posY);
                tile.rotation.x = Math.PI/2;
                break;
            }

              tiles[face][x][y] = tile;
              cubeGroup.add(tile);
              tilesCreated++;
            } catch (error) {
              console.error(`Błąd tworzenia kafelka ${face}[${x}][${y}]:`, error);
            }
          }
        }
      });

      console.log(`Utworzono ${tilesCreated} kafelków`);
      console.log('Dzieci cubeGroup:', cubeGroup.children.length);

      // Cienkie ramki kostki - tylko główne krawędzie
      try {
        const boxGeometry = new THREE.BoxGeometry(7.02, 7.02, 7.02);
        const edgesGeometry = new THREE.EdgesGeometry(boxGeometry);
        const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, opacity: 0.3, transparent: true });
        const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
        cubeGroup.add(cubeEdges);
        console.log('Ramki kostki dodane pomyślnie');
      } catch (error) {
        console.error('Błąd tworzenia ramek kostki:', error);
      }

      console.log('Kostka Rubika z ramkami utworzona!');
      console.log('Finalne dzieci cubeGroup:', cubeGroup.children.length);
      console.log('Pozycja cubeGroup:', cubeGroup.position);
      console.log('Rotacja cubeGroup:', cubeGroup.rotation);
    }

      // Ustaw początkową pozycję kostki - front widoczny
      cubeGroup.rotation.x = 0;
      cubeGroup.rotation.y = 0;
      cubeGroup.rotation.z = 0;

    // Funkcje suwaków
    function updateSliders() {
      const rotXDeg = (cubeGroup.rotation.x * 180 / Math.PI).toFixed(0);
      const rotYDeg = (cubeGroup.rotation.y * 180 / Math.PI).toFixed(0);
      const rotZDeg = (cubeGroup.rotation.z * 180 / Math.PI).toFixed(0);

      document.getElementById('rotationX').value = rotXDeg;
      document.getElementById('rotationY').value = rotYDeg;
      document.getElementById('rotationZ').value = rotZDeg;
      document.getElementById('rotationXValue').textContent = rotXDeg + '°';
      document.getElementById('rotationYValue').textContent = rotYDeg + '°';
      document.getElementById('rotationZValue').textContent = rotZDeg + '°';
    }

    function resetCubeRotation() {
      cubeGroup.rotation.x = 0;
      cubeGroup.rotation.y = 0;
      cubeGroup.rotation.z = 0;
      activeFace = 'front';
      updateSliders();
      updateTiles();
      updatePreviews();
    }

    // ===== FUNKCJE DO ZAZNACZANIA WARSTW RUBIKA =====

    // Mapowanie pozycji kostki na warstwy Rubika
    function getRubikLayers(face, x, y) {
      const layers = [];

      // Konwertuj pozycję lokalną na pozycję globalną w kostce 3x3x3
      const globalPos = getGlobalPosition(face, x, y);

      // Sprawdź wszystkie możliwe warstwy dla tej pozycji
      // R/L warstwy (prawo/lewo) - na podstawie X
      if (globalPos.x === 2) layers.push({ type: 'R', axis: 'x', index: 2, name: 'Prawa (R)' });
      if (globalPos.x === 1) layers.push({ type: 'M', axis: 'x', index: 1, name: 'Środkowa X (M)' });
      if (globalPos.x === 0) layers.push({ type: 'L', axis: 'x', index: 0, name: 'Lewa (L)' });

      // U/D warstwy (góra/dół) - na podstawie Y
      if (globalPos.y === 2) layers.push({ type: 'U', axis: 'y', index: 2, name: 'Górna (U)' });
      if (globalPos.y === 1) layers.push({ type: 'E', axis: 'y', index: 1, name: 'Środkowa Y (E)' });
      if (globalPos.y === 0) layers.push({ type: 'D', axis: 'y', index: 0, name: 'Dolna (D)' });

      // F/B warstwy (przód/tył) - na podstawie Z
      if (globalPos.z === 2) layers.push({ type: 'F', axis: 'z', index: 2, name: 'Przednia (F)' });
      if (globalPos.z === 1) layers.push({ type: 'S', axis: 'z', index: 1, name: 'Środkowa Z (S)' });
      if (globalPos.z === 0) layers.push({ type: 'B', axis: 'z', index: 0, name: 'Tylna (B)' });

      return layers;
    }

    // Konwertuj pozycję lokalną ściany na pozycję globalną w kostce 3x3x3
    function getGlobalPosition(face, x, y) {
      // Pozycje w kostce 3x3x3: 0,1,2 dla każdej osi
      switch(face) {
        case 'front':  // Z = 2
          return { x: x, y: 2-y, z: 2 };
        case 'back':   // Z = 0
          return { x: 2-x, y: 2-y, z: 0 };
        case 'right':  // X = 2
          return { x: 2, y: 2-y, z: 2-x };
        case 'left':   // X = 0
          return { x: 0, y: 2-y, z: x };
        case 'top':    // Y = 2
          return { x: x, y: 2, z: 2-y };
        case 'bottom': // Y = 0
          return { x: x, y: 0, z: y };
        default:
          return { x: 0, y: 0, z: 0 };
      }
    }

    function selectLayerFromCube(cubeInfo) {
      console.log('selectLayerFromCube wywołane:', cubeInfo);
      const { face, x, y } = cubeInfo;

      // Pobierz wszystkie możliwe warstwy dla tej kostki
      const availableLayers = getRubikLayers(face, x, y);
      console.log('Dostępne warstwy:', availableLayers);

      // Sprawdź czy kostka jest już w zaznaczonej warstwie
      if (selectedLayer) {
        const isInCurrentLayer = availableLayers.some(layer =>
          layer.axis === selectedLayer.axis && layer.index === selectedLayer.index
        );

        if (isInCurrentLayer) {
          // Kostka jest w zaznaczonej warstwie - obróć warstwę
          console.log('Kostka w zaznaczonej warstwie - obracam warstwę');
          rotateLayer(selectedLayer.axis, selectedLayer.index, 1);
          return;
        }
      }

      // Wybierz pierwszą dostępną warstwę (domyślnie)
      if (availableLayers.length > 0) {
        const layer = availableLayers[0];
        selectLayer(layer.axis, layer.index, face, x, y, availableLayers);
      }
    }

    function switchLayerType(cubeInfo) {
      console.log('switchLayerType wywołane:', cubeInfo);
      const { face, x, y } = cubeInfo;

      // Pobierz wszystkie możliwe warstwy dla tej kostki
      const availableLayers = getRubikLayers(face, x, y);

      if (!selectedLayer || selectedLayer.face !== face || selectedLayer.x !== x || selectedLayer.y !== y) {
        // Jeśli to inna kostka, zacznij od pierwszej dostępnej warstwy
        if (availableLayers.length > 0) {
          const layer = availableLayers[0];
          selectLayer(layer.axis, layer.index, face, x, y, availableLayers);
        }
        return;
      }

      // Znajdź aktualnie zaznaczoną warstwę w liście dostępnych
      const currentIndex = availableLayers.findIndex(layer =>
        layer.axis === selectedLayer.axis && layer.index === selectedLayer.index
      );

      if (currentIndex !== -1 && availableLayers.length > 1) {
        // Przełącz na następną dostępną warstwę
        const nextIndex = (currentIndex + 1) % availableLayers.length;
        const nextLayer = availableLayers[nextIndex];
        selectLayer(nextLayer.axis, nextLayer.index, face, x, y, availableLayers);
      }
    }

    function selectLayer(axis, index, face, x, y, availableLayers = null) {
      console.log('selectLayer wywołane:', axis, index, face, x, y);

      // Jeśli nie podano dostępnych warstw, oblicz je
      if (!availableLayers) {
        availableLayers = getRubikLayers(face, x, y);
      }

      selectedLayer = {
        axis,
        index,
        face,
        x,
        y,
        availableLayers,
        currentLayerInfo: availableLayers.find(l => l.axis === axis && l.index === index)
      };

      highlightLayer(axis, index);
      updateLayerInfo();

      // Dodaj strzałki kierunków
      showDirectionArrows(axis, index);
    }

    function highlightLayer(axis, index) {
      console.log('highlightLayer wywołane:', axis, index);
      // Usuń poprzednie podświetlenie
      clearHighlight();

      // Znajdź kostki w warstwie używając globalnych pozycji
      Object.keys(faceColors).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const globalPos = getGlobalPosition(face, x, y);
            let inLayer = false;

            // Sprawdź czy kostka należy do zaznaczonej warstwy
            if (axis === 'x' && globalPos.x === index) {
              inLayer = true;
            } else if (axis === 'y' && globalPos.y === index) {
              inLayer = true;
            } else if (axis === 'z' && globalPos.z === index) {
              inLayer = true;
            }

            if (inLayer && tiles[face] && tiles[face][x] && tiles[face][x][y]) {
              const cube = tiles[face][x][y];

              // Jasno białe podświetlenie - dodaj świecącą ramkę
              const edges = new THREE.EdgesGeometry(cube.geometry);
              const lineMaterial = new THREE.LineBasicMaterial({
                color: 0xffffff, // Jasno białe
                linewidth: 4,
                transparent: true,
                opacity: 0.9
              });
              const wireframe = new THREE.LineSegments(edges, lineMaterial);
              wireframe.position.copy(cube.position);
              wireframe.rotation.copy(cube.rotation);
              wireframe.userData.isHighlightEffect = true; // Oznacz jako efekt podświetlenia
              cubeGroup.add(wireframe);

              // Dodaj też delikatne białe świecenie
              const glowGeometry = new THREE.PlaneGeometry(1.8, 1.8);
              const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff, // Jasno białe
                transparent: true,
                opacity: 0.15,
                side: THREE.DoubleSide
              });
              const glow = new THREE.Mesh(glowGeometry, glowMaterial);
              glow.position.copy(cube.position);
              glow.rotation.copy(cube.rotation);
              glow.userData.isHighlightEffect = true; // Oznacz jako efekt podświetlenia
              // Przesuń nieco do przodu
              const normal = new THREE.Vector3(0, 0, 0.01);
              normal.applyQuaternion(cube.quaternion);
              glow.position.add(normal);
              cubeGroup.add(glow);

              // Dodaj tylko efekty podświetlenia, NIE kostki gry
              highlightedCubes.push(wireframe);
              highlightedCubes.push(glow);
            }
          }
        }
      });

      console.log(`Podświetlono ${highlightedCubes.length/2} kostek w warstwie ${axis}[${index}]`);
    }

    function clearHighlight() {
      // Usuń tylko efekty podświetlenia (ramki i świecenie), NIE kostki
      const itemsToRemove = [];

      highlightedCubes.forEach(item => {
        // Sprawdź czy to efekt podświetlenia (nie kostka z grą)
        if (item.userData && item.userData.isHighlightEffect) {
          // To jest efekt podświetlenia - usuń z grupy
          cubeGroup.remove(item);
          itemsToRemove.push(item);
        }
      });

      // Wyczyść tablicę - zostaw tylko prawdziwe kostki gry
      highlightedCubes = highlightedCubes.filter(item => !itemsToRemove.includes(item));

      // Usuń też strzałki kierunków
      clearDirectionArrows();
    }

    function updateLayerInfo() {
      if (selectedLayer) {
        const currentLayer = selectedLayer.currentLayerInfo;
        const availableLayers = selectedLayer.availableLayers;

        let layerInfo = `Zaznaczona warstwa: <strong>${currentLayer.name}</strong>`;

        if (availableLayers.length > 1) {
          const otherLayers = availableLayers
            .filter(l => l.type !== currentLayer.type)
            .map(l => l.name)
            .join(', ');
          layerInfo += `<br><small>Dostępne też: ${otherLayers} (prawy klik aby przełączyć)</small>`;
        }

        document.getElementById('selectedLayerInfo').innerHTML = layerInfo;
        document.getElementById('layerControls').style.display = 'block';
      } else {
        document.getElementById('selectedLayerInfo').innerHTML = 'Kliknij na kostkę żeby zaznaczyć warstwę, potem kliknij strzałki przy kostce';
        document.getElementById('layerControls').style.display = 'none';
      }
    }

    function clearLayerSelection() {
      selectedLayer = null;
      clearHighlight();
      updateLayerInfo();
    }

    function rotateSelectedLayer(direction) {
      console.log('rotateSelectedLayer wywołane:', direction);
      console.log('selectedLayer:', selectedLayer);

      if (!selectedLayer) {
        alert('Najpierw zaznacz warstwę!');
        return;
      }

      console.log('Rozpoczynam obrót warstwy:', selectedLayer.axis, selectedLayer.index, direction);

      try {
        rotateLayer(selectedLayer.axis, selectedLayer.index, direction);
        console.log('Obrót warstwy zakończony pomyślnie');
      } catch (error) {
        console.error('Błąd podczas obrotu warstwy:', error);
      }

      // Wymuś aktualizację podglądów
      updatePreviews();

      // Odśwież podświetlenie po obrocie
      setTimeout(() => {
        console.log('Odświeżanie podświetlenia...');
        highlightLayer(selectedLayer.axis, selectedLayer.index);
        // Odśwież strzałki
        showDirectionArrows(selectedLayer.axis, selectedLayer.index);
      }, 100);
    }

    function showDirectionArrows(axis, index) {
      console.log('showDirectionArrows wywołane:', axis, index);
      clearDirectionArrows();

      // Pokaż intuicyjne strzałki jak w kostce Rubika
      if (axis === 'horizontal') {
        // Warstwa pozioma - pokaż strzałki pokazujące kierunek obrotu warstwy
        createRubikArrow('front', axis, index, 'clockwise', '↻', () => rotateSelectedLayer(1));
        createRubikArrow('front', axis, index, 'counterclockwise', '↺', () => rotateSelectedLayer(-1));
      } else if (axis === 'vertical') {
        // Warstwa pionowa - pokaż strzałki pokazujące kierunek obrotu warstwy
        createRubikArrow('front', axis, index, 'clockwise', '↻', () => rotateSelectedLayer(1));
        createRubikArrow('front', axis, index, 'counterclockwise', '↺', () => rotateSelectedLayer(-1));
      } else if (axis === 'depth') {
        // Warstwa głębokości - pokaż strzałki na odpowiedniej ścianie
        const targetFace = index === 0 ? 'front' : 'back';
        createRubikArrow(targetFace, axis, index, 'clockwise', '↻', () => rotateSelectedLayer(1));
        createRubikArrow(targetFace, axis, index, 'counterclockwise', '↺', () => rotateSelectedLayer(-1));
      }
    }



    function createRubikArrow(face, axis, layerIndex, direction, symbol, onClick) {
      console.log('createRubikArrow:', face, axis, layerIndex, direction, symbol);
      const position = getRubikArrowPosition(face, axis, layerIndex, direction);

      // Utwórz sprite ze strzałką - jasny i czytelny
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = 180;
      canvas.height = 180;

      // Tło - okrągłe z jasnym gradientem
      const gradient = context.createRadialGradient(90, 90, 0, 90, 90, 85);
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
      gradient.addColorStop(0.7, 'rgba(240, 240, 240, 0.9)');
      gradient.addColorStop(1, 'rgba(200, 200, 200, 0.8)');
      context.fillStyle = gradient;
      context.beginPath();
      context.arc(90, 90, 85, 0, Math.PI * 2);
      context.fill();

      // Ramka - ciemna dla kontrastu
      context.strokeStyle = '#333333';
      context.lineWidth = 4;
      context.stroke();

      // Strzałka - duża i ciemna dla czytelności
      context.fillStyle = '#000000';
      context.font = 'Bold 70px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText(symbol, 90, 90);

      const texture = new THREE.CanvasTexture(canvas);
      const spriteMaterial = new THREE.SpriteMaterial({
        map: texture,
        transparent: true
      });
      const sprite = new THREE.Sprite(spriteMaterial);
      sprite.scale.set(1.4, 1.4, 1);
      sprite.position.copy(position);

      // Dodaj interakcję
      sprite.userData = { onClick: onClick, type: 'arrow' };

      cubeGroup.add(sprite);
      directionArrows.push(sprite);
    }

    function getRubikArrowPosition(face, axis, layerIndex, direction) {
      const position = new THREE.Vector3();
      const offset = 2.5; // Większa odległość od kostki

      // Pozycja bazowa ściany
      const facePositions = {
        front: { x: 0, y: 0, z: 1.5 + offset },
        back: { x: 0, y: 0, z: -1.5 - offset },
        left: { x: -1.5 - offset, y: 0, z: 0 },
        right: { x: 1.5 + offset, y: 0, z: 0 },
        top: { x: 0, y: 1.5 + offset, z: 0 },
        bottom: { x: 0, y: -1.5 - offset, z: 0 }
      };

      position.copy(facePositions[face]);

      // Pozycjonuj strzałki w zależności od osi i warstwy
      if (axis === 'horizontal') {
        // Warstwa pozioma - strzałki po bokach warstwy
        position.y = (layerIndex - 1) * 1.0; // -1, 0, 1 dla warstw 0, 1, 2
        if (direction === 'clockwise') position.x += 1.2;
        else if (direction === 'counterclockwise') position.x -= 1.2;
      } else if (axis === 'vertical') {
        // Warstwa pionowa - strzałki po bokach warstwy
        position.x = (layerIndex - 1) * 1.0; // -1, 0, 1 dla warstw 0, 1, 2
        if (direction === 'clockwise') position.y += 1.2;
        else if (direction === 'counterclockwise') position.y -= 1.2;
      } else if (axis === 'depth') {
        // Warstwa głębokości - strzałki na środku ściany
        if (direction === 'clockwise') { position.x += 1.0; position.y += 1.0; }
        else if (direction === 'counterclockwise') { position.x -= 1.0; position.y += 1.0; }
      }

      return position;
    }

    function clearDirectionArrows() {
      directionArrows.forEach(arrow => {
        cubeGroup.remove(arrow);
      });
      directionArrows = [];
    }

    // Udostępnij funkcje globalnie
    window.resetCubeRotation = resetCubeRotation;
    window.selectLayer = selectLayer;
    window.clearLayerSelection = clearLayerSelection;
    window.rotateSelectedLayer = rotateSelectedLayer;
    window.getDepthIndex = getDepthIndex;

    // Obsługa suwaków - bez ograniczeń
    document.getElementById('rotationX').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.x = degrees * Math.PI / 180;
      document.getElementById('rotationXValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    document.getElementById('rotationY').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.y = degrees * Math.PI / 180;
      document.getElementById('rotationYValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    document.getElementById('rotationZ').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.z = degrees * Math.PI / 180;
      document.getElementById('rotationZValue').textContent = degrees + '°';

      // Wykryj aktywną ścianę (obrót Z nie zmienia aktywnej ściany tak bardzo)
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    // Obsługa myszy
    let isDragging = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    renderer.domElement.addEventListener('mousedown', (event) => {
      isDragging = true;
      lastMouseX = event.clientX;
      lastMouseY = event.clientY;
    });

    renderer.domElement.addEventListener('mousemove', (event) => {
      if (!isDragging) return;

      const deltaX = event.clientX - lastMouseX;
      const deltaY = event.clientY - lastMouseY;

      // Sprawdź czy trzymany jest Shift dla obrotu Z
      if (event.shiftKey) {
        cubeGroup.rotation.z += deltaX * 0.01;
      } else {
        cubeGroup.rotation.y += deltaX * 0.01;
        cubeGroup.rotation.x += deltaY * 0.01;
      }

      lastMouseX = event.clientX;
      lastMouseY = event.clientY;

      updateSliders();

      // Wykryj aktywną ścianę podczas przeciągania
      const newActiveFace = detectActiveFace();
      if (newActiveFace !== activeFace) {
        activeFace = newActiveFace;
        updateTiles();
        updatePreviews();
      }
    });

    renderer.domElement.addEventListener('mouseup', () => {
      isDragging = false;
    });

    // Obsługa kliknięć na kostki do zaznaczania warstw
    renderer.domElement.addEventListener('click', (event) => {
      console.log('Kliknięcie lewym przyciskiem');
      if (isDragging) return; // Ignoruj kliknięcia podczas przeciągania

      handleCubeClick(event, false); // false = lewy przycisk
    });

    // Obsługa prawego przycisku myszy do przełączania typu warstwy
    renderer.domElement.addEventListener('contextmenu', (event) => {
      event.preventDefault(); // Wyłącz menu kontekstowe
      if (isDragging) return;

      handleCubeClick(event, true); // true = prawy przycisk
    });

    function handleCubeClick(event, isRightClick) {
      console.log('handleCubeClick wywołane, isRightClick:', isRightClick);

      const rect = renderer.domElement.getBoundingClientRect();
      const mouse = new THREE.Vector2();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(mouse, camera);

      // Znajdź wszystkie obiekty do kliknięcia (kostki + strzałki)
      const allObjects = [];

      // Dodaj kostki
      Object.keys(faceColors).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            if (tiles[face] && tiles[face][x] && tiles[face][x][y]) {
              allObjects.push({
                object: tiles[face][x][y],
                type: 'cube',
                face: face,
                x: x,
                y: y
              });
            }
          }
        }
      });

      // Dodaj strzałki
      directionArrows.forEach(arrow => {
        if (arrow.userData && arrow.userData.type === 'arrow') {
          allObjects.push({
            object: arrow,
            type: 'arrow',
            onClick: arrow.userData.onClick
          });
        }
      });

      const intersects = raycaster.intersectObjects(allObjects.map(item => item.object));

      if (intersects.length > 0) {
        console.log('Znaleziono przecięcie:', intersects.length);
        const clickedObject = intersects[0].object;
        const objectInfo = allObjects.find(item => item.object === clickedObject);

        if (objectInfo) {
          console.log('Kliknięto obiekt:', objectInfo.type, objectInfo);
          if (objectInfo.type === 'cube') {
            if (isRightClick) {
              // Prawy przycisk - przełącz typ warstwy
              switchLayerType(objectInfo);
            } else {
              // Lewy przycisk - zaznacz warstwę poziomą
              selectLayerFromCube(objectInfo);
            }
          } else if (objectInfo.type === 'arrow') {
            objectInfo.onClick();
          }
        }
      } else {
        console.log('Brak przecięć - odznaczam warstwę');
        // Kliknięcie poza kostką - odznacz warstwę
        if (selectedLayer) {
          selectedLayer = null;
          clearHighlight();
          updateLayerInfo();
        }
      }
    }

    renderer.domElement.addEventListener('mouseleave', () => {
      isDragging = false;
    });

    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y];
            const texture = createTileTexture(value, faceColor);
            tiles[face][x][y].material.map = texture;
            tiles[face][x][y].material.needsUpdate = true;
          }
        }
      });

      // Aktualizuj UI
      document.getElementById('score').textContent = score;
      document.getElementById('active').textContent = activeFace.toUpperCase();

      // Aktualizuj podglądy
      if (typeof updatePreviews === 'function') {
        updatePreviews();
      }
    }

    // Mechanika 2048 - ruch w lewo
    function moveLeft() {
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        // Zbierz niepuste kafelki z wiersza
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z prawej strony
        while (row.length < 3) {
          row.push(0);
        }

        // Sprawdź czy coś się zmieniło
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][x][y] = row[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w prawo
    function moveRight() {
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        // Zbierz niepuste kafelki z wiersza (od prawej)
        for (let x = 2; x >= 0; x--) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z lewej strony
        while (row.length < 3) {
          row.push(0);
        }

        // Sprawdź czy coś się zmieniło i ustaw od prawej
        for (let x = 0; x < 3; x++) {
          if (gameData[face][2-x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][2-x][y] = row[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w górę
    function moveUp() {
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        // Zbierz niepuste kafelki z kolumny
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z dołu
        while (col.length < 3) {
          col.push(0);
        }

        // Sprawdź czy coś się zmieniło
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][y] = col[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Mechanika 2048 - ruch w dół
    function moveDown() {
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        // Zbierz niepuste kafelki z kolumny (od dołu)
        for (let y = 2; y >= 0; y--) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z góry
        while (col.length < 3) {
          col.push(0);
        }

        // Sprawdź czy coś się zmieniło i ustaw od dołu
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][2-y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][2-y] = col[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Dodawanie losowego kafelka
    function addRandomTile() {
      const anyFaceSpawn = document.getElementById('anyFaceSpawn').checked;
      const emptyTiles = [];

      if (anyFaceSpawn) {
        // Znajdź puste miejsca na wszystkich ścianach
        Object.keys(gameData).forEach(face => {
          for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
              if (gameData[face][x][y] === 0) {
                emptyTiles.push({face, x, y});
              }
            }
          }
        });
      } else {
        // Znajdź puste miejsca tylko na aktywnej ścianie
        const face = activeFace;
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            if (gameData[face][x][y] === 0) {
              emptyTiles.push({face, x, y});
            }
          }
        }
      }

      if (emptyTiles.length > 0) {
        const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
        // 90% szans na 2, 10% szans na 4
        const value = Math.random() < 0.9 ? 2 : 4;
        gameData[randomTile.face][randomTile.x][randomTile.y] = value;
        updateTiles();

        // Jeśli kafelek pojawił się na nieaktywnej ścianie, pokaż notyfikację
        if (anyFaceSpawn && randomTile.face !== activeFace) {
          showTileNotification(randomTile.face, value);
        }
      }
    }

    // Funkcja do pokazywania notyfikacji o nowym kafelku
    function showTileNotification(face, value) {
      // Znajdź podgląd ściany i dodaj efekt błysku
      const preview = document.querySelector(`[data-face="${face}"]`);
      if (preview) {
        preview.style.boxShadow = '0 0 20px rgba(255, 255, 0, 0.8)';
        preview.style.transform = 'scale(1.1)';

        setTimeout(() => {
          preview.style.boxShadow = '';
          preview.style.transform = '';
        }, 1000);
      }

      // Pokaż krótką notyfikację
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 1000;
        background: rgba(255, 255, 0, 0.9); color: black; padding: 10px 15px;
        border-radius: 8px; font-weight: bold; font-size: 14px;
        transition: all 0.3s ease;
      `;
      notification.textContent = `+${value} na ${face.toUpperCase()}`;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 2000);
    }

    // Sterowanie klawiaturą
    document.addEventListener('keydown', (e) => {
      switch(e.code) {
        case 'ArrowLeft':
          e.preventDefault();
          moveLeft();
          break;
        case 'ArrowRight':
          e.preventDefault();
          moveRight();
          break;
        case 'ArrowUp':
          e.preventDefault();
          moveUp();
          break;
        case 'ArrowDown':
          e.preventDefault();
          moveDown();
          break;
      }
    });

    // Funkcja rysowania podglądu ściany
    function drawFacePreview(face) {
      const canvas = document.getElementById(`canvas-${face}`);
      const ctx = canvas.getContext('2d');

      // Wyczyść canvas
      ctx.clearRect(0, 0, 120, 120);

      // Tło ściany
      ctx.fillStyle = faceColors[face];
      ctx.fillRect(0, 0, 120, 120);

      // Narysuj siatkę 3x3
      const cellSize = 40;
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          const value = gameData[face][x][y];
          const startX = x * cellSize;
          const startY = y * cellSize;

          // Tło kafelka - użyj tileColors zamiast numberColors
          if (value > 0) {
            ctx.fillStyle = tileColors[face][x][y];
          } else {
            ctx.fillStyle = darkenColor(faceColors[face], 0.1);
          }

          // Zaokrąglony kafelek
          ctx.beginPath();
          ctx.roundRect(startX + 2, startY + 2, cellSize - 4, cellSize - 4, 6);
          ctx.fill();

          // Cyfra
          if (value > 0) {
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = getTextColor(value);
            ctx.fillText(value.toString(), startX + cellSize/2, startY + cellSize/2);
          }
        }
      }

      // Ramka aktywnej ściany
      if (face === activeFace) {
        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 3;
        ctx.strokeRect(1, 1, 118, 118);
      }
    }

    // Aktualizacja wszystkich podglądów
    function updatePreviews() {
      Object.keys(faceColors).forEach(face => {
        drawFacePreview(face);
      });

      // Aktualizuj klasy CSS
      document.querySelectorAll('.face-preview').forEach(el => {
        el.classList.remove('active');
      });
      document.querySelector(`[data-face="${activeFace}"]`).classList.add('active');

      // Aktualizuj podświetlenie 3D kostki
      updateCubeHighlight(activeFace);
    }

    // Funkcja do podświetlenia aktywnej ściany na kostce 3D
    function updateCubeHighlight(activeFace) {
      // Resetuj wszystkie materiały do normalnego stanu
      Object.keys(faces).forEach(face => {
        faces[face].forEach(tile => {
          if (tile.material && tile.material.emissive) {
            tile.material.emissive.setHex(0x000000); // Usuń emisję
          }
        });
      });

      // Dodaj delikatne podświetlenie do aktywnej ściany
      if (faces[activeFace]) {
        faces[activeFace].forEach(tile => {
          if (tile.material && tile.material.emissive) {
            tile.material.emissive.setHex(0x222222); // Delikatna emisja
          }
        });
      }
    }

    // Pozycje kostki dla każdej ściany - wyprostowane i skierowane do użytkownika
    const faceRotations = {
      front: { x: -0.1, y: 0.1 },         // Lekko pochylone dla lepszego widoku
      back: { x: -0.1, y: Math.PI + 0.1 }, // Back z przodu + lekkie pochylenie
      left: { x: -0.1, y: Math.PI/2 + 0.1 }, // Left z przodu + pochylenie
      right: { x: -0.1, y: -Math.PI/2 + 0.1 }, // Right z przodu + pochylenie
      top: { x: Math.PI/2 - 0.2, y: 0.1 },     // Top z przodu, mniej pochylony
      // bottom: { x: Math.PI/2 - 0.3, y: 0.2 }  // Bottom - obrót w górę żeby było widać∂
      bottom: { x: -Math.PI/2 + 0.1, y: 0.1 }  // Bottom z przodu - obrót w dół

    };

    // Animacja obrotu kostki
    function animateToFace(targetFace) {
      const target = faceRotations[targetFace];
      const startX = cubeGroup.rotation.x;
      const startY = cubeGroup.rotation.y;
      const duration = 800; // ms
      const startTime = Date.now();

      function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);

        cubeGroup.rotation.x = startX + (target.x - startX) * easeOut;
        cubeGroup.rotation.y = startY + (target.y - startY) * easeOut;

        updateSliders();

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      }

      animate();
    }

    // Wykrywanie aktywnej ściany na podstawie obrotu kostki
    function detectActiveFace() {
      const rotX = cubeGroup.rotation.x;
      const rotY = cubeGroup.rotation.y;

      // Normalizuj kąty do zakresu -π do π
      let normalizedX = ((rotX % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
      if (normalizedX > Math.PI) normalizedX -= 2 * Math.PI;

      let normalizedY = ((rotY % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
      if (normalizedY > Math.PI) normalizedY -= 2 * Math.PI;

      // Progi dla wykrywania ścian
      const threshold = Math.PI / 4; // 45 stopni

      // Sprawdź góra/dół (priorytet dla dużych obrotów X)
      if (normalizedX > threshold) {
        return 'top';    // Kostka obrócona w dół = top widoczny z przodu
      } else if (normalizedX < -threshold) {
        return 'bottom'; // Kostka obrócona w górę = bottom widoczny z przodu
      }

      // Sprawdź boki (dla małych obrotów X)
      if (normalizedY > threshold && normalizedY < Math.PI - threshold) {
        return 'left';   // Kostka obrócona w prawo = left widoczny z przodu
      } else if (normalizedY < -threshold && normalizedY > -Math.PI + threshold) {
        return 'right';  // Kostka obrócona w lewo = right widoczny z przodu
      } else if (Math.abs(normalizedY) > Math.PI - threshold) {
        return 'back';   // Kostka obrócona 180° = back widoczny z przodu
      } else {
        return 'front';  // Domyślnie front
      }
    }

    // Obsługa hover i kliknięć na podglądy ścian
    document.querySelectorAll('.face-preview').forEach(preview => {
      // Hover - podgląd ściany bez zmiany aktywnej ściany
      preview.addEventListener('mouseenter', () => {
        const face = preview.dataset.face;
        animateToFace(face);
      });

      // Kliknięcie - ustaw jako aktywną ścianę
      preview.addEventListener('click', () => {
        const face = preview.dataset.face;
        activeFace = face;
        animateToFace(face);
        updateTiles();
        updatePreviews();
      });
    });







    // POPRAWNE obroty warstw jak w kostce Rubika - tylko dane, bez przebudowy
    function rotateLayer(axis, layerIndex, direction) {
      console.log(`Obracanie warstwy: ${axis}, warstwa ${layerIndex}, kierunek ${direction}`);

      // Obróć tylko dane - kostki pozostają na swoich miejscach
      if (axis === 'x') {
        rotateXLayer(layerIndex, direction);
      } else if (axis === 'y') {
        rotateYLayer(layerIndex, direction);
      } else if (axis === 'z') {
        rotateZLayer(layerIndex, direction);
      }

      // Tylko odśwież tekstury kostek z nowymi danymi
      updateTiles();
      updatePreviews();
    }



    // Obrót warstwy Y (U/E/D - góra/środek/dół)
    function rotateYLayer(layerIndex, direction) {
      console.log(`Obracanie warstwy Y[${layerIndex}], kierunek: ${direction}`);

      // Mapowanie: 0=dół(D), 1=środek(E), 2=góra(U)
      const yPos = layerIndex;

      // Zapisz dane z front
      const tempData = [];
      const tempColors = [];
      for (let x = 0; x < 3; x++) {
        tempData[x] = gameData.front[x][2-yPos]; // Odwrócone Y
        tempColors[x] = tileColors.front[x][2-yPos];
      }

      if (direction === 1) { // Clockwise: front -> left -> back -> right -> front
        for (let x = 0; x < 3; x++) {
          gameData.front[x][2-yPos] = gameData.right[x][2-yPos];
          tileColors.front[x][2-yPos] = tileColors.right[x][2-yPos];

          gameData.right[x][2-yPos] = gameData.back[2-x][2-yPos]; // back odwrócony w X
          tileColors.right[x][2-yPos] = tileColors.back[2-x][2-yPos];

          gameData.back[2-x][2-yPos] = gameData.left[x][2-yPos];
          tileColors.back[2-x][2-yPos] = tileColors.left[x][2-yPos];

          gameData.left[x][2-yPos] = tempData[x];
          tileColors.left[x][2-yPos] = tempColors[x];
        }
      } else { // Counter-clockwise
        for (let x = 0; x < 3; x++) {
          gameData.front[x][2-yPos] = gameData.left[x][2-yPos];
          tileColors.front[x][2-yPos] = tileColors.left[x][2-yPos];

          gameData.left[x][2-yPos] = gameData.back[2-x][2-yPos];
          tileColors.left[x][2-yPos] = tileColors.back[2-x][2-yPos];

          gameData.back[2-x][2-yPos] = gameData.right[x][2-yPos];
          tileColors.back[2-x][2-yPos] = tileColors.right[x][2-yPos];

          gameData.right[x][2-yPos] = tempData[x];
          tileColors.right[x][2-yPos] = tempColors[x];
        }
      }

      // Jeśli obracamy skrajne warstwy, obróć też całą ścianę
      if (layerIndex === 2) { // Górna warstwa - obróć top
        rotateFace('top', direction);
      } else if (layerIndex === 0) { // Dolna warstwa - obróć bottom
        rotateFace('bottom', -direction); // Odwrócony kierunek dla bottom
      }
    }

    // Obrót warstwy X (L/M/R - lewo/środek/prawo)
    function rotateXLayer(layerIndex, direction) {
      console.log(`Obracanie warstwy X[${layerIndex}], kierunek: ${direction}`);

      // Mapowanie: 0=lewo(L), 1=środek(M), 2=prawo(R)
      const xPos = layerIndex;

      // Zapisz dane z front
      const tempData = [];
      const tempColors = [];
      for (let y = 0; y < 3; y++) {
        tempData[y] = gameData.front[xPos][y];
        tempColors[y] = tileColors.front[xPos][y];
      }

      if (direction === 1) { // Clockwise: front -> bottom -> back -> top -> front
        for (let y = 0; y < 3; y++) {
          gameData.front[xPos][y] = gameData.bottom[xPos][y];
          tileColors.front[xPos][y] = tileColors.bottom[xPos][y];

          gameData.bottom[xPos][y] = gameData.back[2-xPos][2-y]; // back odwrócony
          tileColors.bottom[xPos][y] = tileColors.back[2-xPos][2-y];

          gameData.back[2-xPos][2-y] = gameData.top[xPos][y];
          tileColors.back[2-xPos][2-y] = tileColors.top[xPos][y];

          gameData.top[xPos][y] = tempData[y];
          tileColors.top[xPos][y] = tempColors[y];
        }
      } else { // Counter-clockwise
        for (let y = 0; y < 3; y++) {
          gameData.front[xPos][y] = gameData.top[xPos][y];
          tileColors.front[xPos][y] = tileColors.top[xPos][y];

          gameData.top[xPos][y] = gameData.back[2-xPos][2-y];
          tileColors.top[xPos][y] = tileColors.back[2-xPos][2-y];

          gameData.back[2-xPos][2-y] = gameData.bottom[xPos][y];
          tileColors.back[2-xPos][2-y] = tileColors.bottom[xPos][y];

          gameData.bottom[xPos][y] = tempData[y];
          tileColors.bottom[xPos][y] = tempColors[y];
        }
      }

      // Jeśli obracamy skrajne warstwy, obróć też całą ścianę
      if (layerIndex === 0) { // Lewa warstwa - obróć left
        rotateFace('left', -direction); // Odwrócony kierunek dla left
      } else if (layerIndex === 2) { // Prawa warstwa - obróć right
        rotateFace('right', direction);
      }
    }

    // Obrót warstwy Z (B/S/F - tył/środek/przód)
    function rotateZLayer(layerIndex, direction) {
      console.log(`Obracanie warstwy Z[${layerIndex}], kierunek: ${direction}`);

      // Mapowanie: 0=tył(B), 1=środek(S), 2=przód(F)
      if (layerIndex === 2) { // Przednia warstwa (F)
        rotateFace('front', direction);
        rotateEdgesAroundFront(direction);
      } else if (layerIndex === 0) { // Tylna warstwa (B)
        rotateFace('back', -direction); // Odwrócony kierunek dla back
        rotateEdgesAroundBack(direction);
      } else if (layerIndex === 1) { // Środkowa warstwa (S)
        rotateMiddleZLayer(direction);
      }
    }

    // Obrót środkowej warstwy Z (tylko krawędzie środkowe)
    function rotateMiddleZLayer(direction) {
      // Zapisz dane z top (środkowy rząd)
      const tempData = [];
      const tempColors = [];
      for (let x = 0; x < 3; x++) {
        tempData[x] = gameData.top[x][1]; // środkowy rząd Y=1
        tempColors[x] = tileColors.top[x][1];
      }

      if (direction === 1) { // Clockwise: top -> left -> bottom -> right -> top
        for (let x = 0; x < 3; x++) {
          gameData.top[x][1] = gameData.right[1][x]; // right środkowa kolumna
          tileColors.top[x][1] = tileColors.right[1][x];

          gameData.right[1][x] = gameData.bottom[2-x][1]; // bottom odwrócony
          tileColors.right[1][x] = tileColors.bottom[2-x][1];

          gameData.bottom[2-x][1] = gameData.left[1][2-x]; // left środkowa kolumna
          tileColors.bottom[2-x][1] = tileColors.left[1][2-x];

          gameData.left[1][2-x] = tempData[x];
          tileColors.left[1][2-x] = tempColors[x];
        }
      } else { // Counter-clockwise
        for (let x = 0; x < 3; x++) {
          gameData.top[x][1] = gameData.left[1][x];
          tileColors.top[x][1] = tileColors.left[1][x];

          gameData.left[1][x] = gameData.bottom[2-x][1];
          tileColors.left[1][x] = tileColors.bottom[2-x][1];

          gameData.bottom[2-x][1] = gameData.right[1][2-x];
          tileColors.bottom[2-x][1] = tileColors.right[1][2-x];

          gameData.right[1][2-x] = tempData[x];
          tileColors.right[1][2-x] = tempColors[x];
        }
      }
    }

    // Obrót całej ściany 90 stopni
    function rotateFace(face, direction) {
      const data = gameData[face];
      const colors = tileColors[face];

      if (direction === 1) { // Clockwise
        // Obróć dane 90° w prawo
        const newData = [[0,0,0],[0,0,0],[0,0,0]];
        const newColors = [[null,null,null],[null,null,null],[null,null,null]];

        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            newData[y][2-x] = data[x][y];
            newColors[y][2-x] = colors[x][y];
          }
        }

        gameData[face] = newData;
        tileColors[face] = newColors;
      } else { // Counter-clockwise
        // Obróć dane 90° w lewo
        const newData = [[0,0,0],[0,0,0],[0,0,0]];
        const newColors = [[null,null,null],[null,null,null],[null,null,null]];

        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            newData[2-y][x] = data[x][y];
            newColors[2-y][x] = colors[x][y];
          }
        }

        gameData[face] = newData;
        tileColors[face] = newColors;
      }
    }

    function rotateEdgesAroundFront(direction) {
      // Zapisz górną krawędź
      const tempData = [gameData.top[0][2], gameData.top[1][2], gameData.top[2][2]];
      const tempColors = [tileColors.top[0][2], tileColors.top[1][2], tileColors.top[2][2]];

      if (direction === 1) { // Clockwise
        // top <- left <- bottom <- right <- top
        for (let i = 0; i < 3; i++) {
          gameData.top[i][2] = gameData.left[2-i][2];
          tileColors.top[i][2] = tileColors.left[2-i][2];

          gameData.left[2-i][2] = gameData.bottom[2-i][0];
          tileColors.left[2-i][2] = tileColors.bottom[2-i][0];

          gameData.bottom[2-i][0] = gameData.right[i][0];
          tileColors.bottom[2-i][0] = tileColors.right[i][0];

          gameData.right[i][0] = tempData[i];
          tileColors.right[i][0] = tempColors[i];
        }
      } else { // Counter-clockwise
        // top <- right <- bottom <- left <- top
        for (let i = 0; i < 3; i++) {
          gameData.top[i][2] = gameData.right[i][0];
          tileColors.top[i][2] = tileColors.right[i][0];

          gameData.right[i][0] = gameData.bottom[2-i][0];
          tileColors.right[i][0] = tileColors.bottom[2-i][0];

          gameData.bottom[2-i][0] = gameData.left[2-i][2];
          tileColors.bottom[2-i][0] = tileColors.left[2-i][2];

          gameData.left[2-i][2] = tempData[i];
          tileColors.left[2-i][2] = tempColors[i];
        }
      }
    }

    function rotateEdgesAroundBack(direction) {
      // Zapisz górną krawędź
      const tempData = [gameData.top[0][0], gameData.top[1][0], gameData.top[2][0]];
      const tempColors = [tileColors.top[0][0], tileColors.top[1][0], tileColors.top[2][0]];

      if (direction === 1) { // Clockwise
        // top <- right <- bottom <- left <- top
        for (let i = 0; i < 3; i++) {
          gameData.top[i][0] = gameData.right[i][2];
          tileColors.top[i][0] = tileColors.right[i][2];

          gameData.right[i][2] = gameData.bottom[2-i][2];
          tileColors.right[i][2] = tileColors.bottom[2-i][2];

          gameData.bottom[2-i][2] = gameData.left[2-i][0];
          tileColors.bottom[2-i][2] = tileColors.left[2-i][0];

          gameData.left[2-i][0] = tempData[i];
          tileColors.left[2-i][0] = tempColors[i];
        }
      } else { // Counter-clockwise
        // top <- left <- bottom <- right <- top
        for (let i = 0; i < 3; i++) {
          gameData.top[i][0] = gameData.left[2-i][0];
          tileColors.top[i][0] = tileColors.left[2-i][0];

          gameData.left[2-i][0] = gameData.bottom[2-i][2];
          tileColors.left[2-i][0] = tileColors.bottom[2-i][2];

          gameData.bottom[2-i][2] = gameData.right[i][2];
          tileColors.bottom[2-i][2] = tileColors.right[i][2];

          gameData.right[i][2] = tempData[i];
          tileColors.right[i][2] = tempColors[i];
        }
      }
    }

    // Inicjalizacja - sprawdź czy DOM jest gotowy
    console.log('DOM readyState:', document.readyState);
    console.log('Body istnieje:', !!document.body);

    function initializeGame() {
      console.log('=== INICJALIZACJA GRY ===');

        // Ustaw pozycję kamery
        camera.position.set(0, 0, 10);
        camera.lookAt(0, 0, 0);
        console.log('Kamera ustawiona na pozycji:', camera.position);



        // Teraz stwórz prawdziwą kostkę Rubika
        console.log('Tworzę kostkę Rubika...');
        createRubiksCube();

        console.log('Kostka Rubika utworzona');
        console.log('Dzieci sceny:', scene.children.length);

        // Przywróć funkcje aktualizacji
        updateTiles();
        updatePreviews();
        updateSliders();

        // PROSTA animacja
        let frameCount = 0;
        function animate() {
          requestAnimationFrame(animate);

          // Obróć kostki jeśli istnieją
          scene.children.forEach(child => {
            if (child.type === 'Mesh') {
              child.rotation.x += 0.01;
              child.rotation.y += 0.01;
            }
          });

          renderer.render(scene, camera);

          // Log co 120 klatek (co 2 sekundy)
          if (frameCount % 120 === 0) {
            console.log('=== FRAME', frameCount, '===');
            console.log('Dzieci sceny:', scene.children.length);
            console.log('Canvas rozmiar:', renderer.domElement.width, 'x', renderer.domElement.height);
            console.log('Canvas w DOM:', document.body.contains(renderer.domElement));
          }
          frameCount++;
        }

        console.log('Uruchamiam animację...');
        animate();
    }

    // Uruchom inicjalizację po załadowaniu wszystkiego
    window.addEventListener('load', function() {
      console.log('Window load event - wszystko załadowane');
      setTimeout(initializeGame, 100); // Małe opóźnienie dla pewności
    });



      console.log('Kostka gotowa!');
    } // koniec initializeEverything
  </script>
</body>
</html>
