<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2048 3D Kostka Rubika</title>
    <style>
        body {
            margin: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        canvas { display: block; }
        #ui {
            position: absolute; top: 10px; left: 10px; z-index: 100;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div id="ui">
        <h3>2048 3D Kostka Rubika</h3>
        <div>Wynik: <span id="score">0</span></div>
        <div>Status: <span id="status">Ładowanie...</span></div>
        <div style="margin-top: 10px; font-size: 11px;">
            <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
            <div><strong>🖱️ Mysz</strong> - obrót kostki (przeciągnij)</div>
            <div><strong>🔢 Klawisze 1-6</strong> - przełącz ścianę (1=Przód, 2=Tył, 3=Lewo, 4=Prawo, 5=Góra, 6=Dół)</div>
            <div><strong>🔄 Warstwy Rubika:</strong></div>
            <div style="margin-left: 10px; font-size: 10px;">
                <div>• <strong>Lewy klik</strong> na kostce - zaznacz warstwę</div>
                <div>• <strong>Prawy klik</strong> na kostce - przełącz typ warstwy</div>
                <div>• <strong>Klik na strzałki</strong> - obróć zaznaczoną warstwę</div>
            </div>
        </div>

        <div id="layerInfo" style="margin-top: 10px; padding: 10px; background: rgba(255,255,0,0.2); border-radius: 5px; display: none;">
            <div style="font-weight: bold;">Zaznaczona warstwa:</div>
            <div id="layerDetails">Kliknij na kostkę żeby zaznaczyć warstwę</div>
        </div>
    </div>

    <!-- Podglądy ścian -->
    <div id="facePreview" style="position: absolute; top: 10px; right: 10px; z-index: 100;">
        <div style="background: rgba(255,255,255,0.95); padding: 15px; border-radius: 15px; color: #333; box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
            <h4 style="margin: 0 0 10px 0; text-align: center; color: #2c3e50;">Ściany kostki</h4>
            <div id="faceGrid" style="display: grid; grid-template-columns: repeat(3, 60px); gap: 5px; justify-content: center;">
                <!-- Ściany będą dodane dynamicznie -->
            </div>
            <div style="margin-top: 10px; text-align: center; font-size: 12px;">
                <div>Aktywna: <span id="activeFaceDisplay" style="font-weight: bold; color: #ffff99;">PRZÓD</span></div>
                <div style="margin-top: 5px;">Kliknij na podgląd żeby przełączyć</div>
            </div>
        </div>
    </div>

    <!-- Kontrolki obrotów warstw -->
    <div id="layerControls" style="position: absolute; bottom: 10px; right: 10px; z-index: 100; display: none;">
        <div style="background: rgba(255,255,255,0.95); padding: 15px; border-radius: 15px; color: #333; box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
            <h4 style="margin: 0 0 10px 0; text-align: center; color: #2c3e50;">Obrót warstwy</h4>
            <div style="display: flex; gap: 10px; justify-content: center;">
                <button id="rotateLeft" style="padding: 12px 16px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; cursor: pointer; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">↺ Lewo</button>
                <button id="rotateRight" style="padding: 12px 16px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none; border-radius: 10px; cursor: pointer; box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(245, 87, 108, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(245, 87, 108, 0.4)'">↻ Prawo</button>
            </div>
            <div style="text-align: center; margin-top: 10px; font-size: 12px;">
                <button id="layerTypeBtn" style="padding: 8px 12px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; border: none; border-radius: 8px; cursor: pointer; box-shadow: 0 4px 15px rgba(168, 237, 234, 0.4); transition: all 0.3s ease; margin-right: 5px;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(168, 237, 234, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(168, 237, 234, 0.4)'">Typ warstwy: Pozioma</button>
                <button id="clearSelection" style="padding: 8px 12px; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; border: none; border-radius: 8px; cursor: pointer; box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(252, 182, 159, 0.6)'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(252, 182, 159, 0.4)'">Wyczyść zaznaczenie</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        const status = document.getElementById('status');
        
        function updateStatus(msg) {
            console.log(msg);
            status.innerHTML = msg;
        }
        
        updateStatus('Uruchamianie...');
        
        if (typeof THREE === 'undefined') {
            updateStatus('BŁĄD: THREE.js nie załadowane!');
        } else {
            updateStatus('THREE.js OK');
            
            // Zmienne globalne
            let scene, camera, renderer, cubeGroup;
            let gameData = {};
            let tiles = {};
            let activeFace = 'front';
            let score = 0;

            // Nowe zmienne dla funkcjonalności
            let selectedLayer = null;
            let layerType = 'horizontal'; // 'horizontal', 'vertical', 'depth'
            let facePreviewCanvases = {};
            let raycaster, mouse;

            // Funkcje dla warstw - definicja wcześnie

            
            // Kolory ścian - pastelowe jak kostka Rubika
            const faceColors = {
                front: '#87CEEB',   back: '#FFB6C1',   left: '#98FB98',
                right: '#F0E68C',   top: '#DDA0DD',    bottom: '#FFA07A'
            };
            
            // Kolory cyfr - oryginalne z 2048
            const numberColors = {
                2: '#eee4da', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
                32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
                512: '#edc850', 1024: '#edc53f', 2048: '#edc22e', 4096: '#edc22e'
            };

            function getTextColor(value) {
                return [2, 4].includes(value) ? '#776e65' : '#f9f6f2';
            }
            
            function createTileTexture(value, face) {
                const canvas = document.createElement('canvas');
                canvas.width = 256;
                canvas.height = 256;
                const ctx = canvas.getContext('2d');

                // Tło - kolor ściany
                ctx.fillStyle = faceColors[face];
                ctx.fillRect(0, 0, 256, 256);

                // Jeśli jest cyfra
                if (value > 0) {
                    const numberColor = numberColors[value] || '#cdc1b4';
                    const textColor = getTextColor(value);

                    // Kafelek z cyferką - większy z mocno zaokrąglonymi rogami
                    const margin = 12;
                    const tileSize = 256 - 2*margin;
                    const radius = 25;

                    ctx.fillStyle = numberColor;
                    roundRect(ctx, margin, margin, tileSize, tileSize, radius);
                    ctx.fill();

                    // Subtelny cień
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
                    ctx.shadowBlur = 3;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 2;

                    // Cyfra
                    const fontSize = value >= 1000 ? 45 : value >= 100 ? 55 : 65;
                    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = textColor;
                    ctx.shadowColor = 'transparent'; // Wyłącz cień dla tekstu
                    ctx.fillText(value.toString(), 128, 128);
                }

                return new THREE.CanvasTexture(canvas);
            }

            // Funkcja pomocnicza do zaokrąglonych prostokątów
            function roundRect(ctx, x, y, width, height, radius) {
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + width - radius, y);
                ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
                ctx.lineTo(x + width, y + height - radius);
                ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                ctx.lineTo(x + radius, y + height);
                ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
                ctx.lineTo(x, y + radius);
                ctx.quadraticCurveTo(x, y, x + radius, y);
                ctx.closePath();
            }

            
            try {
                updateStatus('Tworzenie sceny...');
                
                // Scena
                scene = new THREE.Scene();
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                renderer = new THREE.WebGLRenderer({
                    antialias: false,
                    alpha: false,
                    precision: 'highp'
                });

                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0xf5f7fa);
                renderer.shadowMap.enabled = false; // Wyłącz cienie żeby usunąć artefakty
                document.body.appendChild(renderer.domElement);
                
                updateStatus('Dodawanie światła...');
                
                // Światło - proste i czyste
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
                directionalLight.position.set(10, 10, 5);
                scene.add(directionalLight);
                
                // Grupa kostki
                cubeGroup = new THREE.Group();
                scene.add(cubeGroup);
                
                updateStatus('Inicjalizacja danych...');
                
                // Inicjalizuj dane gry
                Object.keys(faceColors).forEach(face => {
                    gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
                    tiles[face] = [];
                });
                
                // Dodaj początkowe cyfry
                gameData.front[0][0] = 2;
                gameData.front[2][2] = 4;
                gameData.top[1][1] = 8;
                
                updateStatus('Tworzenie kostki...');

                // Stwórz tła ścian (solidne płaszczyzny z grafitowymi ramkami)
                Object.keys(faceColors).forEach(face => {
                    const bgGeometry = new THREE.PlaneGeometry(4.8, 4.8);
                    const bgMaterial = new THREE.MeshLambertMaterial({
                        color: 0x4a4a4a, // Grafitowy kolor dla ramek
                        side: THREE.FrontSide
                    });
                    const bgPlane = new THREE.Mesh(bgGeometry, bgMaterial);

                    // Pozycjonowanie tła
                    const offset = 2.48; // Nieco za kafelkami
                    switch(face) {
                        case 'front': bgPlane.position.set(0, 0, offset); break;
                        case 'back': bgPlane.position.set(0, 0, -offset); bgPlane.rotation.y = Math.PI; break;
                        case 'left': bgPlane.position.set(-offset, 0, 0); bgPlane.rotation.y = -Math.PI/2; break;
                        case 'right': bgPlane.position.set(offset, 0, 0); bgPlane.rotation.y = Math.PI/2; break;
                        case 'top': bgPlane.position.set(0, offset, 0); bgPlane.rotation.x = -Math.PI/2; break;
                        case 'bottom': bgPlane.position.set(0, -offset, 0); bgPlane.rotation.x = Math.PI/2; break;
                    }
                    cubeGroup.add(bgPlane);
                });

                // Stwórz kafelki
                Object.keys(faceColors).forEach(face => {
                    tiles[face] = [];
                    for (let x = 0; x < 3; x++) {
                        tiles[face][x] = [];
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const texture = createTileTexture(value, face);

                            // Większe kafelki z zaokrąglonymi rogami
                            const geometry = new THREE.PlaneGeometry(1.35, 1.35);
                            const material = new THREE.MeshLambertMaterial({
                                map: texture,
                                transparent: false,
                                side: THREE.FrontSide
                            });

                            const tile = new THREE.Mesh(geometry, material);

                            // Pozycjonowanie z mniejszymi odstępami
                            const posX = (x - 1) * 1.45;
                            const posY = (1 - y) * 1.45;
                            const offset = 2.5;

                            switch(face) {
                                case 'front':
                                    tile.position.set(posX, posY, offset);
                                    break;
                                case 'back':
                                    tile.position.set(-posX, posY, -offset);
                                    tile.rotation.y = Math.PI;
                                    break;
                                case 'right':
                                    tile.position.set(offset, posY, -posX);
                                    tile.rotation.y = Math.PI/2;
                                    break;
                                case 'left':
                                    tile.position.set(-offset, posY, posX);
                                    tile.rotation.y = -Math.PI/2;
                                    break;
                                case 'top':
                                    tile.position.set(posX, offset, posY);
                                    tile.rotation.x = -Math.PI/2;
                                    break;
                                case 'bottom':
                                    tile.position.set(posX, -offset, -posY);
                                    tile.rotation.x = Math.PI/2;
                                    break;
                            }

                            tiles[face][x][y] = tile;
                            cubeGroup.add(tile);
                        }
                    }
                });
                
                updateStatus('Ustawianie kamery...');
                
                // Pozycja kamery
                camera.position.set(8, 6, 8);
                camera.lookAt(0, 0, 0);
                
                updateStatus('Dodawanie kontrolek...');
                
                // Obsługa myszy
                let isDragging = false;
                let hasMoved = false;
                let lastMouseX = 0, lastMouseY = 0;

                renderer.domElement.addEventListener('mousedown', (event) => {
                    isDragging = true;
                    hasMoved = false;
                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });

                renderer.domElement.addEventListener('mousemove', (event) => {
                    if (!isDragging) return;

                    const deltaX = event.clientX - lastMouseX;
                    const deltaY = event.clientY - lastMouseY;

                    // Sprawdź czy mysz się poruszyła znacząco
                    if (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3) {
                        hasMoved = true;
                    }

                    cubeGroup.rotation.y += deltaX * 0.01;
                    cubeGroup.rotation.x += deltaY * 0.01;

                    lastMouseX = event.clientX;
                    lastMouseY = event.clientY;
                });

                renderer.domElement.addEventListener('mouseup', (event) => {
                    isDragging = false;

                    // Jeśli mysz się nie poruszyła znacząco, traktuj jako klik
                    if (!hasMoved) {
                        handleTileClick(event);
                    }
                });

                // Obsługa prawego kliku
                renderer.domElement.addEventListener('contextmenu', (event) => {
                    event.preventDefault();
                    toggleLayerType();
                });

                // Obsługa klawiatury dla gry 2048
                document.addEventListener('keydown', (event) => {
                    switch(event.key) {
                        case 'ArrowLeft':
                            event.preventDefault();
                            moveLeft();
                            break;
                        case 'ArrowRight':
                            event.preventDefault();
                            moveRight();
                            break;
                        case 'ArrowUp':
                            event.preventDefault();
                            moveUp();
                            break;
                        case 'ArrowDown':
                            event.preventDefault();
                            moveDown();
                            break;
                        case '1': case '2': case '3': case '4': case '5': case '6':
                            switchToFace(Object.keys(faceColors)[parseInt(event.key) - 1]);
                            break;
                    }
                });

                updateStatus('Dodawanie funkcji gry...');

                // Inicjalizuj raycaster i mouse
                raycaster = new THREE.Raycaster();
                mouse = new THREE.Vector2();

                // Inicjalizuj podglądy ścian
                createFacePreviews();

                // Inicjalizuj kontrolki warstw
                setupLayerControls();

                updateStatus('Uruchamianie animacji...');
                
                // Animacja
                function animate() {
                    requestAnimationFrame(animate);
                    renderer.render(scene, camera);
                }
                animate();
                
                updateStatus('GOTOWE - kostka powinna być widoczna!');

            } catch (error) {
                updateStatus('BŁĄD: ' + error.message);
                console.error('Błąd inicjalizacji:', error);
            }

            // ===== FUNKCJE GRY 2048 =====

            function updateTiles() {
                if (!tiles || Object.keys(tiles).length === 0) {
                    return;
                }

                Object.keys(faceColors).forEach(face => {
                    if (tiles[face]) {
                        for (let x = 0; x < 3; x++) {
                            for (let y = 0; y < 3; y++) {
                                if (tiles[face][x] && tiles[face][x][y]) {
                                    const value = gameData[face][x][y];
                                    const texture = createTileTexture(value, face);
                                    tiles[face][x][y].material.map = texture;
                                    tiles[face][x][y].material.needsUpdate = true;
                                }
                            }
                        }
                    }
                });
                updateFacePreviews();
                document.getElementById('score').textContent = score;
            }

            function moveLeft() {
                let moved = false;
                const face = activeFace;

                for (let y = 0; y < 3; y++) {
                    const row = [];
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][x][y] !== 0) {
                            row.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < row.length - 1; i++) {
                        if (row[i] === row[i + 1]) {
                            row[i] *= 2;
                            score += row[i];
                            row.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z prawej
                    while (row.length < 3) {
                        row.push(0);
                    }

                    // Sprawdź czy coś się zmieniło
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][x][y] !== row[x]) {
                            moved = true;
                        }
                        gameData[face][x][y] = row[x];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveRight() {
                let moved = false;
                const face = activeFace;

                for (let y = 0; y < 3; y++) {
                    const row = [];
                    for (let x = 2; x >= 0; x--) {
                        if (gameData[face][x][y] !== 0) {
                            row.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < row.length - 1; i++) {
                        if (row[i] === row[i + 1]) {
                            row[i] *= 2;
                            score += row[i];
                            row.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z lewej
                    while (row.length < 3) {
                        row.push(0);
                    }

                    // Sprawdź czy coś się zmieniło i ustaw od prawej
                    for (let x = 0; x < 3; x++) {
                        if (gameData[face][2-x][y] !== row[x]) {
                            moved = true;
                        }
                        gameData[face][2-x][y] = row[x];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveUp() {
                let moved = false;
                const face = activeFace;

                for (let x = 0; x < 3; x++) {
                    const col = [];
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][y] !== 0) {
                            col.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < col.length - 1; i++) {
                        if (col[i] === col[i + 1]) {
                            col[i] *= 2;
                            score += col[i];
                            col.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z dołu
                    while (col.length < 3) {
                        col.push(0);
                    }

                    // Sprawdź czy coś się zmieniło
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][y] !== col[y]) {
                            moved = true;
                        }
                        gameData[face][x][y] = col[y];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function moveDown() {
                let moved = false;
                const face = activeFace;

                for (let x = 0; x < 3; x++) {
                    const col = [];
                    for (let y = 2; y >= 0; y--) {
                        if (gameData[face][x][y] !== 0) {
                            col.push(gameData[face][x][y]);
                        }
                    }

                    // Połącz sąsiadujące kafelki
                    for (let i = 0; i < col.length - 1; i++) {
                        if (col[i] === col[i + 1]) {
                            col[i] *= 2;
                            score += col[i];
                            col.splice(i + 1, 1);
                        }
                    }

                    // Wypełnij zerami z góry
                    while (col.length < 3) {
                        col.push(0);
                    }

                    // Sprawdź czy coś się zmieniło i ustaw od dołu
                    for (let y = 0; y < 3; y++) {
                        if (gameData[face][x][2-y] !== col[y]) {
                            moved = true;
                        }
                        gameData[face][x][2-y] = col[y];
                    }
                }

                if (moved) {
                    updateTiles();
                    setTimeout(addRandomTile, 200);
                }
            }

            function addRandomTile() {
                const emptyTiles = [];

                // Zbierz puste pola ze wszystkich ścian
                Object.keys(faceColors).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            if (gameData[face][x][y] === 0) {
                                emptyTiles.push({face, x, y});
                            }
                        }
                    }
                });

                if (emptyTiles.length > 0) {
                    const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
                    const value = Math.random() < 0.9 ? 2 : 4;
                    gameData[randomTile.face][randomTile.x][randomTile.y] = value;
                    updateTiles();
                }
            }

            // ===== FUNKCJE PODGLĄDÓW ŚCIAN =====

            function createFacePreviews() {
                const faceGrid = document.getElementById('faceGrid');
                const faceNames = Object.keys(faceColors);

                faceNames.forEach((face, index) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 60;
                    canvas.height = 60;
                    canvas.style.border = '2px solid #333';
                    canvas.style.borderRadius = '5px';
                    canvas.style.cursor = 'pointer';
                    const faceNames = {
                        'front': 'PRZÓD',
                        'back': 'TYŁ',
                        'left': 'LEWO',
                        'right': 'PRAWO',
                        'top': 'GÓRA',
                        'bottom': 'DÓŁ'
                    };
                    canvas.title = faceNames[face] || face.toUpperCase();

                    canvas.addEventListener('click', () => switchToFace(face));

                    facePreviewCanvases[face] = canvas;
                    faceGrid.appendChild(canvas);
                });

                updateFacePreviews();
            }

            function updateFacePreviews() {
                Object.keys(faceColors).forEach(face => {
                    const canvas = facePreviewCanvases[face];
                    const ctx = canvas.getContext('2d');

                    // Wyczyść canvas
                    ctx.clearRect(0, 0, 60, 60);

                    // Tło ściany
                    ctx.fillStyle = faceColors[face];
                    ctx.fillRect(0, 0, 60, 60);

                    // Siatka 3x3
                    const tileSize = 18;
                    const margin = 3;

                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            const value = gameData[face][x][y];
                            const posX = margin + x * (tileSize + 1);
                            const posY = margin + y * (tileSize + 1);

                            if (value > 0) {
                                const numberColor = numberColors[value] || '#cdc1b4';
                                ctx.fillStyle = numberColor;
                                roundRect(ctx, posX + 1, posY + 1, tileSize - 2, tileSize - 2, 3);
                                ctx.fill();

                                // Cyfra
                                const fontSize = value >= 1000 ? 8 : value >= 100 ? 10 : 12;
                                ctx.font = `bold ${fontSize}px Arial, sans-serif`;
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'middle';
                                ctx.fillStyle = getTextColor(value);
                                ctx.fillText(value.toString(), posX + tileSize/2, posY + tileSize/2);
                            }
                        }
                    }

                    // Podświetl aktywną ścianę
                    if (face === activeFace) {
                        canvas.style.border = '2px solid #ffff00';
                        canvas.style.boxShadow = '0 0 10px #ffff00';
                    } else {
                        canvas.style.border = '2px solid #333';
                        canvas.style.boxShadow = 'none';
                    }
                });
            }

            function switchToFace(face) {
                if (faceColors[face]) {
                    activeFace = face;
                    const faceNames = {
                        'front': 'PRZÓD',
                        'back': 'TYŁ',
                        'left': 'LEWO',
                        'right': 'PRAWO',
                        'top': 'GÓRA',
                        'bottom': 'DÓŁ'
                    };
                    document.getElementById('activeFaceDisplay').textContent = faceNames[face] || face.toUpperCase();
                    updateFacePreviews();

                    // Animacja obrotu kostki do pokazania aktywnej ściany
                    animateToFace(face);
                }
            }

            function animateToFace(face) {
                // Przestrzenny widok z naciskiem na aktywną ścianę
                const positions = {
                    front: { x: 3, y: 2, z: 8 },
                    back: { x: -3, y: 2, z: -8 },
                    left: { x: -8, y: 2, z: 3 },
                    right: { x: 8, y: 2, z: -3 },
                    top: { x: 2, y: 8, z: 3 },
                    bottom: { x: 2, y: -8, z: 3 }
                };

                const targetPos = positions[face] || positions.front;

                // Poprawione obroty kostki dla właściwej orientacji
                const rotations = {
                    front: { x: 0, y: 0, z: 0 },
                    back: { x: 0, y: Math.PI, z: 0 },
                    left: { x: 0, y: Math.PI/2, z: 0 },
                    right: { x: 0, y: -Math.PI/2, z: 0 },
                    top: { x: Math.PI/2, y: 0, z: 0 },
                    bottom: { x: -Math.PI/2, y: 0, z: 0 }
                };

                const targetRot = rotations[face] || rotations.front;

                // Wyłącz kontrolki podczas animacji
                controls.enabled = false;

                // Animacja kamery i kostki
                const duration = 1000;
                const startPos = { x: camera.position.x, y: camera.position.y, z: camera.position.z };
                const startRot = { x: cubeGroup.rotation.x, y: cubeGroup.rotation.y, z: cubeGroup.rotation.z };
                const startTime = Date.now();

                function animate() {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    const eased = 1 - Math.pow(1 - progress, 3); // easeOut

                    camera.position.x = startPos.x + (targetPos.x - startPos.x) * eased;
                    camera.position.y = startPos.y + (targetPos.y - startPos.y) * eased;
                    camera.position.z = startPos.z + (targetPos.z - startPos.z) * eased;
                    camera.lookAt(0, 0, 0);

                    cubeGroup.rotation.x = startRot.x + (targetRot.x - startRot.x) * eased;
                    cubeGroup.rotation.y = startRot.y + (targetRot.y - startRot.y) * eased;
                    cubeGroup.rotation.z = startRot.z + (targetRot.z - startRot.z) * eased;

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // Włącz kontrolki po zakończeniu animacji
                        controls.enabled = true;
                    }
                }
                animate();
            }

            // ===== FUNKCJE WARSTW RUBIKA =====

            function handleTileClick(event) {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

                raycaster.setFromCamera(mouse, camera);

                // Znajdź wszystkie kafelki
                const allTiles = [];
                Object.keys(tiles).forEach(face => {
                    for (let x = 0; x < 3; x++) {
                        for (let y = 0; y < 3; y++) {
                            allTiles.push({
                                mesh: tiles[face][x][y],
                                face: face,
                                x: x,
                                y: y
                            });
                        }
                    }
                });

                const intersects = raycaster.intersectObjects(allTiles.map(t => t.mesh));

                if (intersects.length > 0) {
                    const clickedTile = allTiles.find(t => t.mesh === intersects[0].object);
                    if (clickedTile) {
                        selectLayer(clickedTile);
                    }
                }
            }
            }

            function selectLayer(tileInfo) {
                selectedLayer = {
                    face: tileInfo.face,
                    index: layerType === 'horizontal' ? tileInfo.y :
                           layerType === 'vertical' ? tileInfo.x :
                           tileInfo.face, // dla depth używamy całej ściany
                    type: layerType
                };

                updateLayerDisplay();
                highlightSelectedLayer();
            }

            function toggleLayerType() {
                const types = ['horizontal', 'vertical', 'depth'];
                const currentIndex = types.indexOf(layerType);
                layerType = types[(currentIndex + 1) % types.length];

                if (selectedLayer) {
                    // Przelicz zaznaczenie dla nowego typu
                    selectedLayer.type = layerType;
                    updateLayerDisplay();
                    highlightSelectedLayer();
                }
            }

            function updateLayerDisplay() {
                const layerInfo = document.getElementById('layerInfo');
                const layerDetails = document.getElementById('layerDetails');
                const layerControls = document.getElementById('layerControls');

                if (selectedLayer) {
                    layerInfo.style.display = 'block';
                    layerControls.style.display = 'block';

                    const typeNames = {
                        horizontal: 'Pozioma',
                        vertical: 'Pionowa',
                        depth: 'Głębokość'
                    };

                    layerDetails.innerHTML = `
                        <div>Ściana: <strong>${selectedLayer.face.toUpperCase()}</strong></div>
                        <div>Typ: <strong>${typeNames[selectedLayer.type]}</strong></div>
                        <div>Indeks: <strong>${selectedLayer.index}</strong></div>
                        <div style="font-size: 11px; margin-top: 5px; color: #ccc;">
                            Prawy klik żeby zmienić typ warstwy
                        </div>
                    `;
                } else {
                    layerInfo.style.display = 'none';
                    layerControls.style.display = 'none';
                }
            }

            function highlightSelectedLayer() {
                // Sprawdź czy tiles są zainicjalizowane
                if (!tiles || Object.keys(tiles).length === 0) {
                    return;
                }

                // Reset wszystkich kafelków
                Object.keys(tiles).forEach(face => {
                    if (tiles[face]) {
                        for (let x = 0; x < 3; x++) {
                            for (let y = 0; y < 3; y++) {
                                if (tiles[face][x] && tiles[face][x][y]) {
                                    tiles[face][x][y].material.emissive.setHex(0x000000);
                                }
                            }
                        }
                    }
                });

                // Podświetl zaznaczoną warstwę
                if (selectedLayer && tiles[selectedLayer.face]) {
                    const { face, index, type } = selectedLayer;

                    if (type === 'horizontal') {
                        // Podświetl cały rząd
                        for (let x = 0; x < 3; x++) {
                            if (tiles[face][x] && tiles[face][x][index]) {
                                tiles[face][x][index].material.emissive.setHex(0x444400);
                            }
                        }
                    } else if (type === 'vertical') {
                        // Podświetl całą kolumnę
                        for (let y = 0; y < 3; y++) {
                            if (tiles[face][index] && tiles[face][index][y]) {
                                tiles[face][index][y].material.emissive.setHex(0x444400);
                            }
                        }
                    } else if (type === 'depth') {
                        // Podświetl całą ścianę
                        for (let x = 0; x < 3; x++) {
                            for (let y = 0; y < 3; y++) {
                                if (tiles[face][x] && tiles[face][x][y]) {
                                    tiles[face][x][y].material.emissive.setHex(0x444400);
                                }
                            }
                        }
                    }
                }
            }

            function setupLayerControls() {
                document.getElementById('rotateLeft').addEventListener('click', () => {
                    if (selectedLayer) {
                        rotateLayer(selectedLayer, -1);
                    }
                });

                document.getElementById('rotateRight').addEventListener('click', () => {
                    if (selectedLayer) {
                        rotateLayer(selectedLayer, 1);
                    }
                });

                document.getElementById('layerTypeBtn').addEventListener('click', toggleLayerType);

                document.getElementById('clearSelection').addEventListener('click', () => {
                    selectedLayer = null;
                    updateLayerDisplay();
                    highlightSelectedLayer();
                });
            }

            function rotateLayer(layer, direction) {
                // Prosta implementacja obrotu warstwy
                const { face, index, type } = layer;

                if (type === 'horizontal') {
                    // Obróć rząd
                    const row = [];
                    for (let x = 0; x < 3; x++) {
                        row.push(gameData[face][x][index]);
                    }

                    if (direction === 1) {
                        // Prawo
                        const temp = row.pop();
                        row.unshift(temp);
                    } else {
                        // Lewo
                        const temp = row.shift();
                        row.push(temp);
                    }

                    for (let x = 0; x < 3; x++) {
                        gameData[face][x][index] = row[x];
                    }
                } else if (type === 'vertical') {
                    // Obróć kolumnę
                    const col = [];
                    for (let y = 0; y < 3; y++) {
                        col.push(gameData[face][index][y]);
                    }

                    if (direction === 1) {
                        // Dół
                        const temp = col.pop();
                        col.unshift(temp);
                    } else {
                        // Góra
                        const temp = col.shift();
                        col.push(temp);
                    }

                    for (let y = 0; y < 3; y++) {
                        gameData[face][index][y] = col[y];
                    }
                }

                updateTiles();
                highlightSelectedLayer();
            }
    </script>
</body>
</html>
