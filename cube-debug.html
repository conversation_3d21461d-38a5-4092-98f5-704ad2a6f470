<!DOCTYPE html>
<html>
<head>
    <title>Debug <PERSON><PERSON><PERSON></title>
    <style>
        body { margin: 0; background: #000; color: white; }
        canvas { display: block; }
        #info { position: absolute; top: 10px; left: 10px; z-index: 100; }
    </style>
</head>
<body>
    <div id="info">
        <h3>Debug Kostka</h3>
        <div id="status">Ładowanie...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        const status = document.getElementById('status');
        
        function updateStatus(msg) {
            console.log(msg);
            status.innerHTML = msg;
        }
        
        updateStatus('Skrypt uruchomiony');
        
        if (typeof THREE === 'undefined') {
            updateStatus('BŁĄD: THREE.js nie załadowane!');
        } else {
            updateStatus('THREE.js OK, wersja: ' + THREE.REVISION);
            
            try {
                // Scena
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer();
                
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x222222);
                document.body.appendChild(renderer.domElement);
                
                updateStatus('Renderer utworzony');
                
                // Kostka
                const geometry = new THREE.BoxGeometry(2, 2, 2);
                const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                // Pozycja kamery
                camera.position.z = 5;
                
                updateStatus('Kostka dodana, rozpoczynam renderowanie');
                
                // Animacja
                function animate() {
                    requestAnimationFrame(animate);
                    
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    
                    renderer.render(scene, camera);
                }
                
                animate();
                updateStatus('Animacja uruchomiona - powinna być widoczna czerwona kostka');
                
            } catch (error) {
                updateStatus('BŁĄD: ' + error.message);
                console.error(error);
            }
        }
    </script>
</body>
</html>
