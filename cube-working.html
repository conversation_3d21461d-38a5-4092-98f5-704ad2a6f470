<!DOCTYPE html>
<html>
<head>
    <title>2048 3D Kostka - WORKING</title>
    <style>
        body { margin: 0; background: #1a1a2e; color: white; font-family: Arial; }
        canvas { display: block; }
        #ui { position: absolute; top: 10px; left: 10px; z-index: 100; 
              background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    </style>
</head>
<body>
    <div id="ui">
        <h3>2048 3D Kostka Rubika</h3>
        <div>Status: <span id="status">Ładowanie...</span></div>
        <div id="info"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        const status = document.getElementById('status');
        const info = document.getElementById('info');
        
        function log(msg) {
            console.log(msg);
            status.innerHTML = msg;
        }
        
        log('Uruchamianie...');
        
        if (typeof THREE === 'undefined') {
            log('BŁĄD: THREE.js nie załadowane!');
        } else {
            log('THREE.js OK');
            
            // Scena
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer();
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x1a1a2e);
            document.body.appendChild(renderer.domElement);
            
            // Grupa kostki
            const cubeGroup = new THREE.Group();
            scene.add(cubeGroup);
            
            // Światło
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Kolory ścian
            const faceColors = {
                front: '#ff9999',   back: '#ffcc99',   left: '#99ccff',
                right: '#99ff99',   top: '#ffff99',    bottom: '#cc99ff'
            };
            
            // Tworzenie kostki
            log('Tworzenie kostki...');
            
            Object.keys(faceColors).forEach(face => {
                for (let x = 0; x < 3; x++) {
                    for (let y = 0; y < 3; y++) {
                        // Prosta geometria i materiał
                        const geometry = new THREE.PlaneGeometry(1.5, 1.5);
                        const material = new THREE.MeshLambertMaterial({ 
                            color: faceColors[face],
                            transparent: true,
                            opacity: 0.8
                        });
                        
                        const tile = new THREE.Mesh(geometry, material);
                        
                        // Pozycjonowanie
                        const posX = (x - 1) * 1.8;
                        const posY = (1 - y) * 1.8;
                        const offset = 2.7;
                        
                        switch(face) {
                            case 'front':
                                tile.position.set(posX, posY, offset);
                                break;
                            case 'back':
                                tile.position.set(-posX, posY, -offset);
                                tile.rotation.y = Math.PI;
                                break;
                            case 'right':
                                tile.position.set(offset, posY, -posX);
                                tile.rotation.y = Math.PI/2;
                                break;
                            case 'left':
                                tile.position.set(-offset, posY, posX);
                                tile.rotation.y = -Math.PI/2;
                                break;
                            case 'top':
                                tile.position.set(posX, offset, posY);
                                tile.rotation.x = -Math.PI/2;
                                break;
                            case 'bottom':
                                tile.position.set(posX, -offset, -posY);
                                tile.rotation.x = Math.PI/2;
                                break;
                        }
                        
                        cubeGroup.add(tile);
                    }
                }
            });
            
            // Ramki kostki
            const edgesGeometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(7, 7, 7));
            const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, opacity: 0.3, transparent: true });
            const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
            cubeGroup.add(cubeEdges);
            
            // Pozycja kamery
            camera.position.set(8, 6, 8);
            camera.lookAt(0, 0, 0);
            
            log('Kostka utworzona, dzieci: ' + cubeGroup.children.length);
            
            // Obsługa myszy
            let isDragging = false;
            let lastMouseX = 0, lastMouseY = 0;
            
            renderer.domElement.addEventListener('mousedown', (event) => {
                isDragging = true;
                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            });
            
            renderer.domElement.addEventListener('mousemove', (event) => {
                if (!isDragging) return;
                
                const deltaX = event.clientX - lastMouseX;
                const deltaY = event.clientY - lastMouseY;
                
                cubeGroup.rotation.y += deltaX * 0.01;
                cubeGroup.rotation.x += deltaY * 0.01;
                
                lastMouseX = event.clientX;
                lastMouseY = event.clientY;
            });
            
            renderer.domElement.addEventListener('mouseup', () => {
                isDragging = false;
            });
            
            // Animacja
            let frameCount = 0;
            function animate() {
                requestAnimationFrame(animate);
                renderer.render(scene, camera);
                
                if (frameCount % 60 === 0) {
                    info.innerHTML = `Frame: ${frameCount}, Dzieci sceny: ${scene.children.length}`;
                }
                frameCount++;
            }
            
            log('Uruchamianie animacji...');
            animate();
            log('GOTOWE - kostka powinna być widoczna');
        }
    </script>
</body>
</html>
