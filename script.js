// Us<PERSON><PERSON><PERSON><PERSON> sceny, kamery i renderera
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setClearColor(0x0f0f23);
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;
document.body.appendChild(renderer.domElement);

// Lepsze oświetlenie
const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
scene.add(ambientLight);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(10, 10, 5);
directionalLight.castShadow = true;
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
scene.add(directionalLight);

// Raycaster do wykrywania aktywnej ściany
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

// Klasa do tworzenia tekstur z cyframi
class TextureGenerator {
    static stworzTekstureZCyfra(cyfra, kolor = '#ffffff', tloKolor = '#2c3e50') {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // Gradient tła
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, tloKolor);
        gradient.addColorStop(1, this.darkenColor(tloKolor, 0.3));
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);
        
        // Elegancka ramka
        ctx.strokeStyle = this.lightenColor(tloKolor, 0.2);
        ctx.lineWidth = 4;
        ctx.strokeRect(4, 4, 248, 248);
        
        // Wewnętrzna ramka
        ctx.strokeStyle = this.darkenColor(tloKolor, 0.2);
        ctx.lineWidth = 2;
        ctx.strokeRect(8, 8, 240, 240);
        
        // Cyfra z cieniem
        if (cyfra > 0) {
            ctx.font = 'bold 64px "Segoe UI", Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Cień
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillText(cyfra.toString(), 130, 130);
            
            // Główna cyfra
            ctx.fillStyle = kolor;
            ctx.fillText(cyfra.toString(), 128, 128);
        }
        
        return new THREE.CanvasTexture(canvas);
    }

    static stworzTekstureScianki(sciana, dane) {
        const canvas = document.createElement('canvas');
        canvas.width = 300;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');
        
        // Kolory ścian
        const koloryScian = {
            'front': '#e74c3c',   'back': '#f39c12',    'left': '#3498db',
            'right': '#2ecc71',   'top': '#f1c40f',     'bottom': '#9b59b6'
        };
        
        // Gradient tła
        const gradient = ctx.createLinearGradient(0, 0, 300, 300);
        gradient.addColorStop(0, koloryScian[sciana]);
        gradient.addColorStop(1, this.darkenColor(koloryScian[sciana], 0.3));
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 300, 300);
        
        // Siatka 3x3
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.lineWidth = 3;
        for (let i = 0; i <= 3; i++) {
            ctx.beginPath();
            ctx.moveTo(i * 100, 0);
            ctx.lineTo(i * 100, 300);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(0, i * 100);
            ctx.lineTo(300, i * 100);
            ctx.stroke();
        }
        
        // Cyfry
        ctx.font = 'bold 32px "Segoe UI", Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                if (dane[x] && dane[x][y] && dane[x][y].wartosc > 0) {
                    // Cień
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
                    ctx.fillText(dane[x][y].wartosc.toString(), x * 100 + 52, y * 100 + 52);
                    
                    // Główna cyfra
                    ctx.fillStyle = '#ffffff';
                    ctx.fillText(dane[x][y].wartosc.toString(), x * 100 + 50, y * 100 + 50);
                }
            }
        }
        
        return new THREE.CanvasTexture(canvas);
    }

    static darkenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }

    static lightenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
        const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
        const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }
}

// Klasa Kafelek
class Kafelek {
    constructor(wartosc = 0, pozycja = { x: 0, y: 0, z: 0 }, sciana = 'front') {
        this.wartosc = wartosc;
        this.pozycja = pozycja;
        this.sciana = sciana;
        this.mesh = null;
        this.stworzMesh();
    }

    stworzMesh() {
        const geometria = new THREE.PlaneGeometry(0.9, 0.9);
        const kolor = this.pobierzKolor();
        const tekstura = TextureGenerator.stworzTekstureZCyfra(this.wartosc, '#ffffff', kolor);
        
        const material = new THREE.MeshLambertMaterial({ 
            map: tekstura,
            transparent: this.wartosc === 0,
            opacity: this.wartosc === 0 ? 0.2 : 1.0  // Półprzeźroczyste puste pola
        });
        
        this.mesh = new THREE.Mesh(geometria, material);
        this.mesh.userData = { sciana: this.sciana, kafelek: this };
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.ustawPozycjeNaSciane();
    }

    ustawPozycjeNaSciane() {
        if (!this.mesh) return;
        
        const offset = 2.41; // Powiększone o 60%
        const spacing = 1.6; // Większy odstęp między kafelkami
        
        switch(this.sciana) {
            case 'front':
                this.mesh.position.set(this.pozycja.x * spacing, this.pozycja.y * spacing, offset);
                this.mesh.rotation.set(0, 0, 0);
                break;
            case 'back':
                this.mesh.position.set(-this.pozycja.x * spacing, this.pozycja.y * spacing, -offset);
                this.mesh.rotation.set(0, Math.PI, 0);
                break;
            case 'right':
                this.mesh.position.set(offset, this.pozycja.y * spacing, -this.pozycja.x * spacing);
                this.mesh.rotation.set(0, Math.PI/2, 0);
                break;
            case 'left':
                this.mesh.position.set(-offset, this.pozycja.y * spacing, this.pozycja.x * spacing);
                this.mesh.rotation.set(0, -Math.PI/2, 0);
                break;
            case 'top':
                this.mesh.position.set(this.pozycja.x * spacing, offset, -this.pozycja.y * spacing);
                this.mesh.rotation.set(-Math.PI/2, 0, 0);
                break;
            case 'bottom':
                this.mesh.position.set(this.pozycja.x * spacing, -offset, this.pozycja.y * spacing);
                this.mesh.rotation.set(Math.PI/2, 0, 0);
                break;
        }
    }

    pobierzKolor() {
        const kolory = {
            0: '#2c3e50',
            2: '#f39c12',    4: '#e67e22',    8: '#e74c3c',    16: '#c0392b',
            32: '#9b59b6',   64: '#8e44ad',   128: '#3498db',  256: '#2980b9',
            512: '#1abc9c',  1024: '#16a085', 2048: '#27ae60', 4096: '#2ecc71'
        };
        return kolory[this.wartosc] || '#34495e';
    }

    aktualizujWartosc(nowaWartosc) {
        this.wartosc = nowaWartosc;
        if (this.mesh) {
            const kolor = this.pobierzKolor();
            const tekstura = TextureGenerator.stworzTekstureZCyfra(this.wartosc, '#ffffff', kolor);
            this.mesh.material.map = tekstura;
            this.mesh.material.transparent = this.wartosc === 0;
            this.mesh.material.opacity = this.wartosc === 0 ? 0.2 : 1.0;  // Półprzeźroczyste puste pola
            this.mesh.material.needsUpdate = true;
        }
    }
}

// Klasa Kostka3D
class Kostka3D {
    constructor() {
        this.rozmiar = 3;
        this.sciany = this.inicjalizujSciany();
        this.grupaKostki = new THREE.Group();
        this.wynik = 0;
        this.aktywnaSciana = 'front';
        this.scianySześcianu = {};
        this.stworzStrukture();
        this.dodajLosowyKafelek();
        this.dodajLosowyKafelek();
    }

    inicjalizujSciany() {
        const nazwyScian = ['front', 'back', 'left', 'right', 'top', 'bottom'];
        const sciany = {};
        
        nazwyScian.forEach(sciana => {
            sciany[sciana] = [];
            for (let x = 0; x < this.rozmiar; x++) {
                sciany[sciana][x] = [];
                for (let y = 0; y < this.rozmiar; y++) {
                    const pozycja = {
                        x: (x - 1) * 1.0,
                        y: (y - 1) * 1.0,
                        z: 0
                    };
                    sciany[sciana][x][y] = new Kafelek(0, pozycja, sciana);
                }
            }
        });
        
        return sciany;
    }

    stworzStrukture() {
        // Eleganckie kolory ścian kostki Rubika
        const koloryScian = {
            'front': 0xdc3545,   'back': 0xfd7e14,    'left': 0x0d6efd,
            'right': 0x198754,   'top': 0xffc107,     'bottom': 0x6f42c1
        };

        const geometriaScian = new THREE.PlaneGeometry(4.8, 4.8); // Powiększone o 60%
        const offset = 2.4; // Powiększone o 60%

        Object.keys(koloryScian).forEach(sciana => {
            const material = new THREE.MeshLambertMaterial({
                color: koloryScian[sciana],
                transparent: true,
                opacity: 0.9,
                side: THREE.DoubleSide
            });
            
            const mesh = new THREE.Mesh(geometriaScian, material);
            mesh.userData = { sciana: sciana, typ: 'sciana' };
            mesh.receiveShadow = true;
            
            switch(sciana) {
                case 'front':
                    mesh.position.set(0, 0, offset);
                    break;
                case 'back':
                    mesh.position.set(0, 0, -offset);
                    mesh.rotation.y = Math.PI;
                    break;
                case 'right':
                    mesh.position.set(offset, 0, 0);
                    mesh.rotation.y = Math.PI/2;
                    break;
                case 'left':
                    mesh.position.set(-offset, 0, 0);
                    mesh.rotation.y = -Math.PI/2;
                    break;
                case 'top':
                    mesh.position.set(0, offset, 0);
                    mesh.rotation.x = -Math.PI/2;
                    break;
                case 'bottom':
                    mesh.position.set(0, -offset, 0);
                    mesh.rotation.x = Math.PI/2;
                    break;
            }
            
            this.scianySześcianu[sciana] = mesh;
            this.grupaKostki.add(mesh);
        });

        // Dodanie kafelków
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].mesh) {
                        this.grupaKostki.add(this.sciany[nazwaSciana][x][y].mesh);
                    }
                }
            }
        });

        scene.add(this.grupaKostki);
    }

    podswietlSciane(sciana) {
        // Resetuj wszystkie ściany
        Object.keys(this.scianySześcianu).forEach(nazwa => {
            this.scianySześcianu[nazwa].material.opacity = 0.9;
        });
        
        // Podświetl aktywną
        if (this.scianySześcianu[sciana]) {
            this.scianySześcianu[sciana].material.opacity = 1.0;
        }
        
        this.aktywnaSciana = sciana;
    }

    dodajLosowyKafelek() {
        const pustePozycje = [];
        
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].wartosc === 0) {
                        pustePozycje.push({ sciana: nazwaSciana, x, y });
                    }
                }
            }
        });

        if (pustePozycje.length > 0) {
            const losowaPoz = pustePozycje[Math.floor(Math.random() * pustePozycje.length)];
            const wartosc = Math.random() < 0.9 ? 2 : 4;
            
            this.sciany[losowaPoz.sciana][losowaPoz.x][losowaPoz.y].aktualizujWartosc(wartosc);
        }
    }

    przesunWierszLubKolumne(kierunek) {
        let ruchWykonany = false;
        const sciana = this.sciany[this.aktywnaSciana];
        
        if (kierunek === 'up' || kierunek === 'down') {
            for (let x = 0; x < this.rozmiar; x++) {
                const kolumna = [];
                for (let y = 0; y < this.rozmiar; y++) {
                    kolumna.push(sciana[x][y].wartosc);
                }
                
                // Naprawka: odwrócenie logiki dla góra/dół
                const nowaKolumna = this.przetworzLinie2048(kolumna, kierunek === 'down');
                
                if (JSON.stringify(kolumna) !== JSON.stringify(nowaKolumna)) {
                    for (let y = 0; y < this.rozmiar; y++) {
                        sciana[x][y].aktualizujWartosc(nowaKolumna[y]);
                    }
                    ruchWykonany = true;
                }
            }
        } else if (kierunek === 'left' || kierunek === 'right') {
            for (let y = 0; y < this.rozmiar; y++) {
                const wiersz = [];
                for (let x = 0; x < this.rozmiar; x++) {
                    wiersz.push(sciana[x][y].wartosc);
                }
                
                const nowyWiersz = this.przetworzLinie2048(wiersz, kierunek === 'left');
                
                if (JSON.stringify(wiersz) !== JSON.stringify(nowyWiersz)) {
                    for (let x = 0; x < this.rozmiar; x++) {
                        sciana[x][y].aktualizujWartosc(nowyWiersz[x]);
                    }
                    ruchWykonany = true;
                }
            }
        }

        if (ruchWykonany) {
            setTimeout(() => {
                this.dodajLosowyKafelek();
            }, 300);
        }
        
        return ruchWykonany;
    }

    przetworzLinie2048(linia, doLewej) {
        // Usuń zera
        let bezZer = linia.filter(val => val !== 0);
        
        if (!doLewej) {
            bezZer.reverse();
        }
        
        // Połącz sąsiadujące jednakowe liczby
        const wynik = [];
        let i = 0;
        
        while (i < bezZer.length) {
            if (i < bezZer.length - 1 && bezZer[i] === bezZer[i + 1]) {
                const nowaWartosc = bezZer[i] * 2;
                wynik.push(nowaWartosc);
                this.wynik += nowaWartosc;
                i += 2;
            } else {
                wynik.push(bezZer[i]);
                i++;
            }
        }
        
        // Dodaj zera
        while (wynik.length < this.rozmiar) {
            wynik.push(0);
        }
        
        if (!doLewej) {
            wynik.reverse();
        }
        
        return wynik;
    }

    sprawdzCzy2048() {
        return Object.keys(this.sciany).some(nazwaSciana => {
            return this.sciany[nazwaSciana].some(kolumna => {
                return kolumna.some(kafelek => kafelek.wartosc === 2048);
            });
        });
    }

    sprawdzKoniecGry() {
        return this.policzPustePola() === 0 && !this.sprawdzMozliweRuchy();
    }

    sprawdzMozliweRuchy() {
        return Object.keys(this.sciany).some(nazwaSciana => {
            const sciana = this.sciany[nazwaSciana];
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    const wartosc = sciana[x][y].wartosc;
                    if (wartosc === 0) return true;
                    
                    const sasiedzi = [
                        [x+1, y], [x-1, y], [x, y+1], [x, y-1]
                    ];
                    
                    for (let [sx, sy] of sasiedzi) {
                        if (sx >= 0 && sx < this.rozmiar && sy >= 0 && sy < this.rozmiar) {
                            if (sciana[sx][sy].wartosc === wartosc) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        });
    }

    policzPustePola() {
        let puste = 0;
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].wartosc === 0) {
                        puste++;
                    }
                }
            }
        });
        return puste;
    }

    obrocWarstwe(warstwa, kierunek) {
        // Prawdziwy obrót warstwy jak w kostce Rubika
        const wartosciDoObrotu = this.zbierzWartosciWarstwy(warstwa);
        const noweWartosci = this.obrocWartosciWarstwy(wartosciDoObrotu, kierunek);
        this.ustawWartosciWarstwy(warstwa, noweWartosci);

        // Dodatkowo obróć samą ścianę górną/dolną jeśli obracamy te warstwy
        if (warstwa === 'top') {
            this.obrocSciane('top', kierunek);
        } else if (warstwa === 'bottom') {
            this.obrocSciane('bottom', kierunek);
        }

        // Zaktualizuj podgląd natychmiast
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }

    zbierzWartosciWarstwy(warstwa) {
        const wartosci = {};
        
        switch(warstwa) {
            case 'top': // Górna warstwa (y = 1)
                wartosci.front = [this.sciany.front[0][2].wartosc, this.sciany.front[1][2].wartosc, this.sciany.front[2][2].wartosc];
                wartosci.right = [this.sciany.right[0][2].wartosc, this.sciany.right[1][2].wartosc, this.sciany.right[2][2].wartosc];
                wartosci.back = [this.sciany.back[0][2].wartosc, this.sciany.back[1][2].wartosc, this.sciany.back[2][2].wartosc];
                wartosci.left = [this.sciany.left[0][2].wartosc, this.sciany.left[1][2].wartosc, this.sciany.left[2][2].wartosc];
                break;
            case 'middle': // Środkowa warstwa (y = 0)
                wartosci.front = [this.sciany.front[0][1].wartosc, this.sciany.front[1][1].wartosc, this.sciany.front[2][1].wartosc];
                wartosci.right = [this.sciany.right[0][1].wartosc, this.sciany.right[1][1].wartosc, this.sciany.right[2][1].wartosc];
                wartosci.back = [this.sciany.back[0][1].wartosc, this.sciany.back[1][1].wartosc, this.sciany.back[2][1].wartosc];
                wartosci.left = [this.sciany.left[0][1].wartosc, this.sciany.left[1][1].wartosc, this.sciany.left[2][1].wartosc];
                break;
            case 'bottom': // Dolna warstwa (y = -1)
                wartosci.front = [this.sciany.front[0][0].wartosc, this.sciany.front[1][0].wartosc, this.sciany.front[2][0].wartosc];
                wartosci.right = [this.sciany.right[0][0].wartosc, this.sciany.right[1][0].wartosc, this.sciany.right[2][0].wartosc];
                wartosci.back = [this.sciany.back[0][0].wartosc, this.sciany.back[1][0].wartosc, this.sciany.back[2][0].wartosc];
                wartosci.left = [this.sciany.left[0][0].wartosc, this.sciany.left[1][0].wartosc, this.sciany.left[2][0].wartosc];
                break;
        }
        
        return wartosci;
    }

    obrocWartosciWarstwy(wartosci, kierunek) {
        const sciany = ['front', 'right', 'back', 'left'];
        const noweWartosci = {};
        
        for (let i = 0; i < sciany.length; i++) {
            const obecnaSciana = sciany[i];
            const nastepnaSciana = sciany[(i + kierunek + sciany.length) % sciany.length];
            noweWartosci[obecnaSciana] = wartosci[nastepnaSciana];
        }
        
        return noweWartosci;
    }

    ustawWartosciWarstwy(warstwa, wartosci) {
        switch(warstwa) {
            case 'top':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][2].aktualizujWartosc(wartosci.front[x]);
                    this.sciany.right[x][2].aktualizujWartosc(wartosci.right[x]);
                    this.sciany.back[x][2].aktualizujWartosc(wartosci.back[x]);
                    this.sciany.left[x][2].aktualizujWartosc(wartosci.left[x]);
                }
                break;
            case 'middle':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][1].aktualizujWartosc(wartosci.front[x]);
                    this.sciany.right[x][1].aktualizujWartosc(wartosci.right[x]);
                    this.sciany.back[x][1].aktualizujWartosc(wartosci.back[x]);
                    this.sciany.left[x][1].aktualizujWartosc(wartosci.left[x]);
                }
                break;
            case 'bottom':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][0].aktualizujWartosc(wartosci.front[x]);
                    this.sciany.right[x][0].aktualizujWartosc(wartosci.right[x]);
                    this.sciany.back[x][0].aktualizujWartosc(wartosci.back[x]);
                    this.sciany.left[x][0].aktualizujWartosc(wartosci.left[x]);
                }
                break;
        }
    }

    // Nowa metoda do obracania samej ściany (top/bottom)
    obrocSciane(sciana, kierunek) {
        const stareWartosci = [];

        // Zbierz wartości w kolejności: [0,0], [0,1], [0,2], [1,2], [2,2], [2,1], [2,0], [1,0]
        // To jest kolejność obwodu ściany 3x3
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                stareWartosci[x * 3 + y] = this.sciany[sciana][x][y].wartosc;
            }
        }

        // Obrót macierzy 3x3
        const noweWartosci = [];
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                if (kierunek === 1) { // Obrót w prawo (zgodnie z ruchem wskazówek zegara)
                    noweWartosci[x * 3 + y] = stareWartosci[(2 - y) * 3 + x];
                } else { // Obrót w lewo (przeciwnie do ruchu wskazówek zegara)
                    noweWartosci[x * 3 + y] = stareWartosci[y * 3 + (2 - x)];
                }
            }
        }

        // Ustaw nowe wartości
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                this.sciany[sciana][x][y].aktualizujWartosc(noweWartosci[x * 3 + y]);
            }
        }
    }

    // Metoda do obracania warstw pionowych (left/right/center)
    obrocWarstweVertical(warstwa, kierunek) {
        const wartosciDoObrotu = this.zbierzWartosciWarstwaPionowa(warstwa);
        const noweWartosci = this.obrocWartosciWarstwaPionowa(wartosciDoObrotu, kierunek);
        this.ustawWartosciWarstwaPionowa(warstwa, noweWartosci);

        // Dodatowo obróć samą ścianę left/right jeśli obracamy te warstwy
        if (warstwa === 'left') {
            this.obrocSciane('left', kierunek);
        } else if (warstwa === 'right') {
            this.obrocSciane('right', kierunek);
        }

        // Zaktualizuj podgląd natychmiast
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }

    zbierzWartosciWarstwaPionowa(warstwa) {
        const wartosci = {};

        switch(warstwa) {
            case 'left': // Lewa warstwa (x = 0)
                wartosci.front = [this.sciany.front[0][0].wartosc, this.sciany.front[0][1].wartosc, this.sciany.front[0][2].wartosc];
                wartosci.top = [this.sciany.top[0][0].wartosc, this.sciany.top[0][1].wartosc, this.sciany.top[0][2].wartosc];
                wartosci.back = [this.sciany.back[2][0].wartosc, this.sciany.back[2][1].wartosc, this.sciany.back[2][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[0][2].wartosc, this.sciany.bottom[0][1].wartosc, this.sciany.bottom[0][0].wartosc];
                break;
            case 'center': // Środkowa warstwa pionowa (x = 1)
                wartosci.front = [this.sciany.front[1][0].wartosc, this.sciany.front[1][1].wartosc, this.sciany.front[1][2].wartosc];
                wartosci.top = [this.sciany.top[1][0].wartosc, this.sciany.top[1][1].wartosc, this.sciany.top[1][2].wartosc];
                wartosci.back = [this.sciany.back[1][0].wartosc, this.sciany.back[1][1].wartosc, this.sciany.back[1][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[1][2].wartosc, this.sciany.bottom[1][1].wartosc, this.sciany.bottom[1][0].wartosc];
                break;
            case 'right': // Prawa warstwa (x = 2)
                wartosci.front = [this.sciany.front[2][0].wartosc, this.sciany.front[2][1].wartosc, this.sciany.front[2][2].wartosc];
                wartosci.top = [this.sciany.top[2][0].wartosc, this.sciany.top[2][1].wartosc, this.sciany.top[2][2].wartosc];
                wartosci.back = [this.sciany.back[0][0].wartosc, this.sciany.back[0][1].wartosc, this.sciany.back[0][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[2][2].wartosc, this.sciany.bottom[2][1].wartosc, this.sciany.bottom[2][0].wartosc];
                break;
        }

        return wartosci;
    }

    obrocWartosciWarstwaPionowa(wartosci, kierunek) {
        const sciany = ['front', 'top', 'back', 'bottom'];
        const noweWartosci = {};

        for (let i = 0; i < sciany.length; i++) {
            const obecnaSciana = sciany[i];
            const nastepnaSciana = sciany[(i + kierunek + sciany.length) % sciany.length];
            noweWartosci[obecnaSciana] = wartosci[nastepnaSciana];
        }

        return noweWartosci;
    }

    ustawWartosciWarstwaPionowa(warstwa, wartosci) {
        switch(warstwa) {
            case 'left':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[0][y].aktualizujWartosc(wartosci.front[y]);
                    this.sciany.top[0][y].aktualizujWartosc(wartosci.top[y]);
                    this.sciany.back[2][y].aktualizujWartosc(wartosci.back[y]);
                    this.sciany.bottom[0][2-y].aktualizujWartosc(wartosci.bottom[y]);
                }
                break;
            case 'center':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[1][y].aktualizujWartosc(wartosci.front[y]);
                    this.sciany.top[1][y].aktualizujWartosc(wartosci.top[y]);
                    this.sciany.back[1][y].aktualizujWartosc(wartosci.back[y]);
                    this.sciany.bottom[1][2-y].aktualizujWartosc(wartosci.bottom[y]);
                }
                break;
            case 'right':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[2][y].aktualizujWartosc(wartosci.front[y]);
                    this.sciany.top[2][y].aktualizujWartosc(wartosci.top[y]);
                    this.sciany.back[0][y].aktualizujWartosc(wartosci.back[y]);
                    this.sciany.bottom[2][2-y].aktualizujWartosc(wartosci.bottom[y]);
                }
                break;
        }
    }
}

// Klasa MenedżerGry
class MenedzerGry {
    constructor() {
        this.kostka = new Kostka3D();
        this.konfiguracjaSterowania();
        this.stworzUI();
        this.stworzPodgladScian();
        this.aktualizujUI();
        this.stanGry = 'gra';
        this.obracanie = false;
        this.ostatniaPozycjaMyszy = { x: 0, y: 0 };
    }

    konfiguracjaSterowania() {
        // Hover dla aktywacji ściany - tylko gdy nie obracamy
        document.addEventListener('mousemove', (event) => {
            if (isDragging) return; // Nie zmieniaj aktywnej ściany podczas obracania

            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(this.kostka.grupaKostki.children);

            if (intersects.length > 0) {
                const obj = intersects[0].object;
                if (obj.userData.typ === 'sciana') {
                    this.kostka.podswietlSciane(obj.userData.sciana);
                    this.aktualizujUI();
                    this.aktualizujPodgladScian();
                }
            }
        });

        // Klawiatura
        document.addEventListener('keydown', (event) => {
            if (this.stanGry !== 'gra') return;
            
            switch(event.code) {
                case 'ArrowUp':
                    this.kostka.przesunWierszLubKolumne('up');
                    break;
                case 'ArrowDown':
                    this.kostka.przesunWierszLubKolumne('down');
                    break;
                case 'ArrowLeft':
                    this.kostka.przesunWierszLubKolumne('left');
                    break;
                case 'ArrowRight':
                    this.kostka.przesunWierszLubKolumne('right');
                    break;
                case 'Space':
                    event.preventDefault();
                    this.kostka.dodajLosowyKafelek();
                    break;
                case 'KeyR':
                    this.nowaGra();
                    break;
                // Obrót warstw kostki Rubika
                case 'KeyQ':
                    this.kostka.obrocWarstwe('top', 1);
                    break;
                case 'KeyE':
                    this.kostka.obrocWarstwe('top', -1);
                    break;
                case 'KeyA':
                    this.kostka.obrocWarstweVertical('left', 1);
                    break;
                case 'KeyD':
                    this.kostka.obrocWarstweVertical('right', 1);
                    break;
                case 'KeyW':
                    this.kostka.obrocWarstwe('middle', 1);
                    break;
                case 'KeyS':
                    this.kostka.obrocWarstwe('bottom', 1);
                    break;
                case 'KeyZ':
                    this.kostka.obrocWarstweVertical('center', 1);
                    break;
            }
            
            this.sprawdzStanGry();
            this.aktualizujUI();
            this.aktualizujPodgladScian();
        });

        // Obracanie kostki myszą - poprawione sterowanie
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };

        document.addEventListener('mousedown', (event) => {
            if (event.button === 0) {
                isDragging = true;
                previousMousePosition.x = event.clientX;
                previousMousePosition.y = event.clientY;
                document.body.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            document.body.style.cursor = 'default';
        });

        document.addEventListener('mousemove', (event) => {
            if (!isDragging) return;

            const deltaX = event.clientX - previousMousePosition.x;
            const deltaY = event.clientY - previousMousePosition.y;

            // Poprawione sterowanie - bardziej intuicyjne i płynne
            this.kostka.grupaKostki.rotation.y += deltaX * 0.008;
            this.kostka.grupaKostki.rotation.x -= deltaY * 0.008; // Odwrócone dla bardziej naturalnego sterowania

            // Ograniczenie obrotu w pionie
            this.kostka.grupaKostki.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x));

            previousMousePosition.x = event.clientX;
            previousMousePosition.y = event.clientY;

            // Automatycznie aktualizuj aktywną ścianę podczas obracania
            this.aktualizujAktywnaSciane();
        });

        // Touch events - poprawione
        document.addEventListener('touchstart', (event) => {
            if (event.touches.length === 1) {
                isDragging = true;
                previousMousePosition.x = event.touches[0].clientX;
                previousMousePosition.y = event.touches[0].clientY;
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });

        document.addEventListener('touchmove', (event) => {
            if (!isDragging || event.touches.length !== 1) return;

            event.preventDefault();
            const deltaX = event.touches[0].clientX - previousMousePosition.x;
            const deltaY = event.touches[0].clientY - previousMousePosition.y;

            this.kostka.grupaKostki.rotation.y += deltaX * 0.008;
            this.kostka.grupaKostki.rotation.x -= deltaY * 0.008; // Odwrócone dla bardziej naturalnego sterowania

            // Ograniczenie obrotu w pionie
            this.kostka.grupaKostki.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x));

            previousMousePosition.x = event.touches[0].clientX;
            previousMousePosition.y = event.touches[0].clientY;

            // Automatycznie aktualizuj aktywną ścianę podczas obracania
            this.aktualizujAktywnaSciane();
        });

        document.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    stworzPodgladScian() {
        const podglad = document.createElement('div');
        podglad.id = 'podgladScian';
        podglad.innerHTML = `
            <h3>Podgląd wszystkich ścian</h3>
            <div class="sciany-grid">
                ${['front', 'back', 'left', 'right', 'top', 'bottom'].map(sciana => `
                    <div class="sciana-podglad">
                        <div class="nazwa-sciany">${sciana.toUpperCase()}</div>
                        <canvas id="canvas-${sciana}" width="300" height="300"></canvas>
                    </div>
                `).join('')}
            </div>
        `;
        
        document.body.appendChild(podglad);
        this.aktualizujPodgladScian();
    }

    aktualizujPodgladScian() {
        // Kolory ścian kostki
        const koloryScian = {
            'front': '#dc3545',   'back': '#fd7e14',    'left': '#0d6efd',
            'right': '#198754',   'top': '#ffc107',     'bottom': '#6f42c1'
        };

        Object.keys(this.kostka.sciany).forEach(sciana => {
            const canvas = document.getElementById(`canvas-${sciana}`);
            const scianaPodglad = canvas?.parentElement;

            if (canvas && scianaPodglad) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Podświetl aktywną ścianę
                if (sciana === this.kostka.aktywnaSciana) {
                    scianaPodglad.classList.add('active');
                } else {
                    scianaPodglad.classList.remove('active');
                }

                // Tło ściany z gradientem
                const gradient = ctx.createLinearGradient(0, 0, 300, 300);
                gradient.addColorStop(0, koloryScian[sciana]);
                gradient.addColorStop(1, this.darkenColor(koloryScian[sciana], 0.2));
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 300, 300);

                // Rysuj siatkę 3x3
                for (let x = 0; x < 3; x++) {
                    for (let y = 0; y < 3; y++) {
                        const wartosc = this.kostka.sciany[sciana][x][y].wartosc;
                        const rozmiarKomorki = 100;
                        const startX = x * rozmiarKomorki;
                        const startY = y * rozmiarKomorki;

                        // Tło kafelka - jeśli jest wartość, użyj koloru cyfry, inaczej kolor ściany
                        if (wartosc > 0) {
                            const kolor = this.pobierzKolorDlaWartosci(wartosc);

                            // Gradient dla kafelka
                            const kafelekGradient = ctx.createRadialGradient(
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, 0,
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, rozmiarKomorki/2
                            );
                            kafelekGradient.addColorStop(0, kolor);
                            kafelekGradient.addColorStop(1, this.darkenColor(kolor, 0.3));

                            ctx.fillStyle = kafelekGradient;
                            ctx.fillRect(startX + 8, startY + 8, rozmiarKomorki - 16, rozmiarKomorki - 16);

                            // Cień tekstu dla lepszej czytelności
                            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                            ctx.font = 'bold 32px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(wartosc.toString(), startX + rozmiarKomorki/2 + 1, startY + rozmiarKomorki/2 + 1);

                            // Główny tekst - biały z obramowaniem
                            ctx.fillStyle = '#ffffff';
                            ctx.strokeStyle = '#000000';
                            ctx.lineWidth = 2;
                            ctx.strokeText(wartosc.toString(), startX + rozmiarKomorki/2, startY + rozmiarKomorki/2);
                            ctx.fillText(wartosc.toString(), startX + rozmiarKomorki/2, startY + rozmiarKomorki/2);
                        } else {
                            // Puste pole - półprzeźroczyste z wzorem
                            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                            ctx.fillRect(startX + 8, startY + 8, rozmiarKomorki - 16, rozmiarKomorki - 16);

                            // Wzór dla pustych pól
                            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                            ctx.lineWidth = 1;
                            ctx.setLineDash([5, 5]);
                            ctx.strokeRect(startX + 8, startY + 8, rozmiarKomorki - 16, rozmiarKomorki - 16);
                            ctx.setLineDash([]);
                        }

                        // Ramka komórki
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(startX + 2, startY + 2, rozmiarKomorki - 4, rozmiarKomorki - 4);
                    }
                }

                // Podświetl aktywną ścianę
                if (sciana === this.kostka.aktywnaSciana) {
                    ctx.strokeStyle = '#ffff00';
                    ctx.lineWidth = 6;
                    ctx.strokeRect(3, 3, 294, 294);
                }
            }
        });
    }

    // Pomocnicza funkcja do ciemnienia kolorów
    darkenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }

    pobierzKolorDlaWartosci(wartosc) {
        const kolory = {
            2: '#eee4da', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
            32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
            512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
        };
        return kolory[wartosc] || '#3c3a32';
    }

    sprawdzStanGry() {
        if (this.kostka.sprawdzCzy2048() && this.stanGry === 'gra') {
            this.stanGry = 'wygrana';
            this.pokazKomunikat('🎉 Gratulacje! Osiągnąłeś 2048!', '#27ae60');
        } else if (this.kostka.sprawdzKoniecGry()) {
            this.stanGry = 'przegrana';
            this.pokazKomunikat('💀 Koniec gry! Brak możliwych ruchów.', '#e74c3c');
        }
    }

    pokazKomunikat(tekst, kolor) {
        const komunikat = document.createElement('div');
        komunikat.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: ${kolor};
            color: white;
            padding: 30px 50px;
            border-radius: 20px;
            font-size: 28px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        `;
        komunikat.textContent = tekst;
        
        document.body.appendChild(komunikat);
        
        setTimeout(() => {
            if (document.body.contains(komunikat)) {
                document.body.removeChild(komunikat);
            }
        }, 4000);
    }

    stworzUI() {
        const ui = document.createElement('div');
        ui.id = 'gameUI';
        ui.innerHTML = `
            <div class="score">� Wynik: <span id="wynik">0</span></div>
            <div class="score">📦 Puste pola: <span id="pustePola">54</span></div>
            <div class="active-face">🎯 Aktywna ściana: <span id="aktywnaSciana">FRONT</span></div>
            
            <!-- Strzałki sterowania kostką -->
            <div class="kostka-controls">
                <div class="rotation-controls">
                    <button id="rotateUp" class="control-btn">⬆️</button>
                    <div class="horizontal-controls">
                        <button id="rotateLeft" class="control-btn">⬅️</button>
                        <span class="control-label">Obrót kostki</span>
                        <button id="rotateRight" class="control-btn">➡️</button>
                    </div>
                    <button id="rotateDown" class="control-btn">⬇️</button>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <strong>🎮 Sterowanie gry:</strong>
                    <div>🖱️ Najedź myszą - wybór ściany</div>
                    <div>⬅️➡️⬆️⬇️ Strzałki - przesuwanie cyfr</div>
                    <div>🖱️ Przeciągnij - obrót kostki</div>
                </div>
                
                <div class="control-group">
                    <strong>🔄 Obrót warstw (jak Rubik):</strong>
                    <div>Q/E - górna warstwa</div>
                    <div>A/D - lewa/prawa warstwa</div>
                    <div>W/S - środkowa/dolna warstwa</div>
                    <div>Z - środkowa warstwa pionowa</div>
                </div>
                
                <div class="control-group">
                    <div>⭐ Spacja - dodaj kafelek</div>
                    <div>🔄 R - nowa gra</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(ui);
        
        // Dodaj event listenery dla przycisków
        this.dodajEventListeneryPrzyciskow();
    }

    dodajEventListeneryPrzyciskow() {
        document.getElementById('rotateLeft').addEventListener('click', () => {
            this.animujObrot('y', -Math.PI / 4);
        });

        document.getElementById('rotateRight').addEventListener('click', () => {
            this.animujObrot('y', Math.PI / 4);
        });

        document.getElementById('rotateUp').addEventListener('click', () => {
            const nowyKat = Math.max(-Math.PI/2, this.kostka.grupaKostki.rotation.x - Math.PI / 4);
            this.animujObrot('x', nowyKat - this.kostka.grupaKostki.rotation.x);
        });

        document.getElementById('rotateDown').addEventListener('click', () => {
            const nowyKat = Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x + Math.PI / 4);
            this.animujObrot('x', nowyKat - this.kostka.grupaKostki.rotation.x);
        });
    }

    // Płynna animacja obrotu
    animujObrot(os, deltaKat) {
        const startTime = Date.now();
        const duration = 300; // 300ms animacji
        const startRotation = this.kostka.grupaKostki.rotation[os];
        const targetRotation = startRotation + deltaKat;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function dla płynniejszej animacji
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            this.kostka.grupaKostki.rotation[os] = startRotation + (deltaKat * easeProgress);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.kostka.grupaKostki.rotation[os] = targetRotation;
                this.aktualizujAktywnaSciane();
            }
        };

        animate();
    }

    aktualizujAktywnaSciane() {
        // Automatycznie wybierz najbardziej widoczną ścianę na podstawie rotacji
        const rotY = this.kostka.grupaKostki.rotation.y;
        const rotX = this.kostka.grupaKostki.rotation.x;

        let sciana = 'front';

        // Sprawdź czy patrzymy głównie z góry lub z dołu
        if (rotX < -Math.PI/4) {
            sciana = 'top';
        } else if (rotX > Math.PI/4) {
            sciana = 'bottom';
        } else {
            // Normalizuj rotację Y do zakresu 0-2π
            const normalizedY = ((rotY % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);

            // Określ ścianę na podstawie kąta obrotu
            if (normalizedY >= 0 && normalizedY < Math.PI/4) {
                sciana = 'front';
            } else if (normalizedY >= Math.PI/4 && normalizedY < 3*Math.PI/4) {
                sciana = 'right';
            } else if (normalizedY >= 3*Math.PI/4 && normalizedY < 5*Math.PI/4) {
                sciana = 'back';
            } else if (normalizedY >= 5*Math.PI/4 && normalizedY < 7*Math.PI/4) {
                sciana = 'left';
            } else {
                sciana = 'front';
            }
        }

        this.kostka.podswietlSciane(sciana);
        this.aktualizujUI();
        this.aktualizujPodgladScian();
    }

    aktualizujUI() {
        document.getElementById('wynik').textContent = this.kostka.wynik.toLocaleString();
        document.getElementById('pustePola').textContent = this.kostka.policzPustePola();
        document.getElementById('aktywnaSciana').textContent = this.kostka.aktywnaSciana.toUpperCase();
    }

    nowaGra() {
        scene.remove(this.kostka.grupaKostki);
        this.kostka = new Kostka3D();
        this.stanGry = 'gra';
        this.aktualizujUI();
        this.aktualizujPodgladScian();
    }
}

// Inicjalizacja gry
const gra = new MenedzerGry();
window.gra = gra; // Dodaj do window dla dostępu globalnego

// Pozycja kamery - dostosowana do większej kostki
camera.position.set(12, 9, 12); // Powiększone o 60%
camera.lookAt(0, 0, 0);

// Pętla animacji
function animuj() {
    requestAnimationFrame(animuj);
    renderer.render(scene, camera);
}
animuj();

