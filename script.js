// Us<PERSON><PERSON><PERSON><PERSON> sceny, kamery i renderera
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setClearColor(0x0f0f23);
renderer.shadowMap.enabled = true;
renderer.shadowMap.type = THREE.PCFSoftShadowMap;
document.body.appendChild(renderer.domElement);

// Lepsze oświetlenie
const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
scene.add(ambientLight);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(10, 10, 5);
directionalLight.castShadow = true;
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
scene.add(directionalLight);

// Raycaster do wykrywania aktywnej ściany
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

// Klasa do tworzenia tekstur z cyframi
class TextureGenerator {
    static stworzTekstureZCyfra(cyfra, kolor = '#ffffff', tloKolor = '#2c3e50', kolorSciany = '#2c3e50') {
        const canvas = document.createElement('canvas');
        canvas.width = 512; // Większa rozdzielczość dla lepszej czytelności
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // Użyj koloru ściany jako tła dla pustych pól
        const finalTloKolor = cyfra > 0 ? tloKolor : kolorSciany;

        // Gradient tła
        const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
        gradient.addColorStop(0, finalTloKolor);
        gradient.addColorStop(1, this.darkenColor(finalTloKolor, 0.3));
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);

        // Elegancka ramka - grubsza dla lepszej widoczności
        ctx.strokeStyle = this.lightenColor(finalTloKolor, 0.3);
        ctx.lineWidth = 8;
        ctx.strokeRect(8, 8, 496, 496);

        // Wewnętrzna ramka
        ctx.strokeStyle = this.darkenColor(finalTloKolor, 0.4);
        ctx.lineWidth = 4;
        ctx.strokeRect(16, 16, 480, 480);

        // Cyfra z cieniem - większa czcionka
        if (cyfra > 0) {
            ctx.font = 'bold 120px "Arial Black", Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Podwójny cień dla lepszej czytelności
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillText(cyfra.toString(), 260, 260);
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fillText(cyfra.toString(), 258, 258);

            // Główna cyfra z obramowaniem
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.lineWidth = 6;
            ctx.strokeText(cyfra.toString(), 256, 256);

            ctx.fillStyle = kolor;
            ctx.fillText(cyfra.toString(), 256, 256);
        }

        return new THREE.CanvasTexture(canvas);
    }

    static stworzTekstureScianki(sciana, dane) {
        const canvas = document.createElement('canvas');
        canvas.width = 300;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');
        
        // Kolory ścian
        const koloryScian = {
            'front': '#e74c3c',   'back': '#f39c12',    'left': '#3498db',
            'right': '#2ecc71',   'top': '#f1c40f',     'bottom': '#9b59b6'
        };
        
        // Gradient tła
        const gradient = ctx.createLinearGradient(0, 0, 300, 300);
        gradient.addColorStop(0, koloryScian[sciana]);
        gradient.addColorStop(1, this.darkenColor(koloryScian[sciana], 0.3));
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 300, 300);
        
        // Siatka 3x3
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.lineWidth = 3;
        for (let i = 0; i <= 3; i++) {
            ctx.beginPath();
            ctx.moveTo(i * 100, 0);
            ctx.lineTo(i * 100, 300);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(0, i * 100);
            ctx.lineTo(300, i * 100);
            ctx.stroke();
        }
        
        // Cyfry
        ctx.font = 'bold 32px "Segoe UI", Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                if (dane[x] && dane[x][y] && dane[x][y].wartosc > 0) {
                    // Cień
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
                    ctx.fillText(dane[x][y].wartosc.toString(), x * 100 + 52, y * 100 + 52);
                    
                    // Główna cyfra
                    ctx.fillStyle = '#ffffff';
                    ctx.fillText(dane[x][y].wartosc.toString(), x * 100 + 50, y * 100 + 50);
                }
            }
        }
        
        return new THREE.CanvasTexture(canvas);
    }

    static darkenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }

    static lightenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
        const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
        const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }
}

// Klasa Kafelek
class Kafelek {
    constructor(wartosc = 0, pozycja = { x: 0, y: 0, z: 0 }, sciana = 'front', kolorSciany = '#ffffff') {
        this.wartosc = wartosc;
        this.pozycja = pozycja;
        this.sciana = sciana;
        this.kolorSciany = kolorSciany; // Kolor bazowy ściany dla tego kafelka
        this.mesh = null;
        this.stworzMesh();
    }

    stworzMesh() {
        const geometria = new THREE.PlaneGeometry(1.8, 1.8); // Powiększone kafelki
        const kolor = this.wartosc > 0 ? this.pobierzKolor() : this.kolorSciany;
        const tekstura = TextureGenerator.stworzTekstureZCyfra(this.wartosc, '#ffffff', kolor, this.kolorSciany);

        const material = new THREE.MeshLambertMaterial({
            map: tekstura,
            transparent: this.wartosc === 0,
            opacity: this.wartosc === 0 ? 0.8 : 1.0  // Mniej przezroczyste puste pola
        });

        this.mesh = new THREE.Mesh(geometria, material);
        this.mesh.userData = { sciana: this.sciana, kafelek: this };
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.ustawPozycjeNaSciane();
    }

    ustawPozycjeNaSciane() {
        if (!this.mesh) return;

        const offset = 4.0; // Większa kostka
        const spacing = 2.2; // Większy odstęp między kafelkami
        
        switch(this.sciana) {
            case 'front':
                this.mesh.position.set(this.pozycja.x * spacing, this.pozycja.y * spacing, offset);
                this.mesh.rotation.set(0, 0, 0);
                break;
            case 'back':
                this.mesh.position.set(-this.pozycja.x * spacing, this.pozycja.y * spacing, -offset);
                this.mesh.rotation.set(0, Math.PI, 0);
                break;
            case 'right':
                this.mesh.position.set(offset, this.pozycja.y * spacing, -this.pozycja.x * spacing);
                this.mesh.rotation.set(0, Math.PI/2, 0);
                break;
            case 'left':
                this.mesh.position.set(-offset, this.pozycja.y * spacing, this.pozycja.x * spacing);
                this.mesh.rotation.set(0, -Math.PI/2, 0);
                break;
            case 'top':
                this.mesh.position.set(this.pozycja.x * spacing, offset, -this.pozycja.y * spacing);
                this.mesh.rotation.set(-Math.PI/2, 0, 0);
                break;
            case 'bottom':
                this.mesh.position.set(this.pozycja.x * spacing, -offset, this.pozycja.y * spacing);
                this.mesh.rotation.set(Math.PI/2, 0, 0);
                break;
        }
    }

    pobierzKolor() {
        const kolory = {
            0: '#2c3e50',
            2: '#f39c12',    4: '#e67e22',    8: '#e74c3c',    16: '#c0392b',
            32: '#9b59b6',   64: '#8e44ad',   128: '#3498db',  256: '#2980b9',
            512: '#1abc9c',  1024: '#16a085', 2048: '#27ae60', 4096: '#2ecc71'
        };
        return kolory[this.wartosc] || '#34495e';
    }

    aktualizujWartosc(nowaWartosc, nowyKolorSciany = null) {
        this.wartosc = nowaWartosc;
        if (nowyKolorSciany) {
            this.kolorSciany = nowyKolorSciany;
        }
        if (this.mesh) {
            const kolor = this.wartosc > 0 ? this.pobierzKolor() : this.kolorSciany;
            const tekstura = TextureGenerator.stworzTekstureZCyfra(this.wartosc, '#ffffff', kolor, this.kolorSciany);
            this.mesh.material.map = tekstura;
            this.mesh.material.transparent = this.wartosc === 0;
            this.mesh.material.opacity = this.wartosc === 0 ? 0.8 : 1.0;
            this.mesh.material.needsUpdate = true;
        }
    }
}

// Klasa Kostka3D
class Kostka3D {
    constructor() {
        this.rozmiar = 3;
        this.sciany = this.inicjalizujSciany();
        this.grupaKostki = new THREE.Group();
        this.wynik = 0;
        this.aktywnaSciana = 'front';
        this.scianySześcianu = {};
        // Kolory bazowe ścian - będą się obracać razem z wartościami
        this.koloryScian = {
            'front': '#dc3545',   'back': '#fd7e14',    'left': '#0d6efd',
            'right': '#198754',   'top': '#ffc107',     'bottom': '#6f42c1'
        };
        this.stworzStrukture();
        this.dodajLosowyKafelek();
        this.dodajLosowyKafelek();
    }

    inicjalizujSciany() {
        const nazwyScian = ['front', 'back', 'left', 'right', 'top', 'bottom'];
        const sciany = {};

        nazwyScian.forEach(sciana => {
            sciany[sciana] = [];
            for (let x = 0; x < this.rozmiar; x++) {
                sciany[sciana][x] = [];
                for (let y = 0; y < this.rozmiar; y++) {
                    const pozycja = {
                        x: (x - 1) * 1.0,
                        y: (y - 1) * 1.0,
                        z: 0
                    };
                    // Każdy kafelek dostaje kolor swojej ściany
                    const kolorSciany = this.koloryScian[sciana];
                    sciany[sciana][x][y] = new Kafelek(0, pozycja, sciana, kolorSciany);
                }
            }
        });

        return sciany;
    }

    stworzStrukture() {
        // Użyj kolorów z this.koloryScian i konwertuj na hex
        const koloryScianHex = {};
        Object.keys(this.koloryScian).forEach(sciana => {
            koloryScianHex[sciana] = parseInt(this.koloryScian[sciana].replace('#', '0x'));
        });

        const geometriaScian = new THREE.PlaneGeometry(8.0, 8.0); // Większa kostka
        const offset = 4.0; // Większy offset

        Object.keys(koloryScianHex).forEach(sciana => {
            const material = new THREE.MeshLambertMaterial({
                color: koloryScianHex[sciana],
                transparent: true,
                opacity: 0.9,
                side: THREE.DoubleSide
            });
            
            const mesh = new THREE.Mesh(geometriaScian, material);
            mesh.userData = { sciana: sciana, typ: 'sciana' };
            mesh.receiveShadow = true;
            
            switch(sciana) {
                case 'front':
                    mesh.position.set(0, 0, offset);
                    break;
                case 'back':
                    mesh.position.set(0, 0, -offset);
                    mesh.rotation.y = Math.PI;
                    break;
                case 'right':
                    mesh.position.set(offset, 0, 0);
                    mesh.rotation.y = Math.PI/2;
                    break;
                case 'left':
                    mesh.position.set(-offset, 0, 0);
                    mesh.rotation.y = -Math.PI/2;
                    break;
                case 'top':
                    mesh.position.set(0, offset, 0);
                    mesh.rotation.x = -Math.PI/2;
                    break;
                case 'bottom':
                    mesh.position.set(0, -offset, 0);
                    mesh.rotation.x = Math.PI/2;
                    break;
            }
            
            this.scianySześcianu[sciana] = mesh;
            this.grupaKostki.add(mesh);
        });

        // Dodanie kafelków
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].mesh) {
                        this.grupaKostki.add(this.sciany[nazwaSciana][x][y].mesh);
                    }
                }
            }
        });

        // Dodaj linie podziału na kostce
        this.dodajLiniePodzialu();

        scene.add(this.grupaKostki);

        // Upewnij się, że kolory są prawidłowo ustawione
        this.aktualizujKoloryScian3D();
    }

    dodajLiniePodzialu() {
        const material = new THREE.LineBasicMaterial({
            color: 0x000000,
            linewidth: 3,
            transparent: true,
            opacity: 0.8
        });

        const offset = 4.0;
        const spacing = 2.2;

        // Dla każdej ściany dodaj linie podziału
        const sciany = [
            { name: 'front', pos: [0, 0, offset], rot: [0, 0, 0] },
            { name: 'back', pos: [0, 0, -offset], rot: [0, Math.PI, 0] },
            { name: 'right', pos: [offset, 0, 0], rot: [0, Math.PI/2, 0] },
            { name: 'left', pos: [-offset, 0, 0], rot: [0, -Math.PI/2, 0] },
            { name: 'top', pos: [0, offset, 0], rot: [-Math.PI/2, 0, 0] },
            { name: 'bottom', pos: [0, -offset, 0], rot: [Math.PI/2, 0, 0] }
        ];

        sciany.forEach(sciana => {
            // Linie pionowe
            for (let i = -1; i <= 1; i += 2) {
                const geometry = new THREE.BufferGeometry().setFromPoints([
                    new THREE.Vector3(i * spacing / 2, -offset/2, 0),
                    new THREE.Vector3(i * spacing / 2, offset/2, 0)
                ]);
                const line = new THREE.Line(geometry, material);
                line.position.set(...sciana.pos);
                line.rotation.set(...sciana.rot);
                this.grupaKostki.add(line);
            }

            // Linie poziome
            for (let i = -1; i <= 1; i += 2) {
                const geometry = new THREE.BufferGeometry().setFromPoints([
                    new THREE.Vector3(-offset/2, i * spacing / 2, 0),
                    new THREE.Vector3(offset/2, i * spacing / 2, 0)
                ]);
                const line = new THREE.Line(geometry, material);
                line.position.set(...sciana.pos);
                line.rotation.set(...sciana.rot);
                this.grupaKostki.add(line);
            }
        });
    }

    podswietlSciane(sciana) {
        // Resetuj wszystkie ściany
        Object.keys(this.scianySześcianu).forEach(nazwa => {
            this.scianySześcianu[nazwa].material.opacity = 0.9;
        });
        
        // Podświetl aktywną
        if (this.scianySześcianu[sciana]) {
            this.scianySześcianu[sciana].material.opacity = 1.0;
        }
        
        this.aktywnaSciana = sciana;
    }

    dodajLosowyKafelek() {
        const pustePozycje = [];
        
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].wartosc === 0) {
                        pustePozycje.push({ sciana: nazwaSciana, x, y });
                    }
                }
            }
        });

        if (pustePozycje.length > 0) {
            const losowaPoz = pustePozycje[Math.floor(Math.random() * pustePozycje.length)];
            const wartosc = Math.random() < 0.9 ? 2 : 4;
            
            this.sciany[losowaPoz.sciana][losowaPoz.x][losowaPoz.y].aktualizujWartosc(wartosc);
        }
    }

    przesunWierszLubKolumne(kierunek) {
        let ruchWykonany = false;
        const sciana = this.sciany[this.aktywnaSciana];
        
        if (kierunek === 'up' || kierunek === 'down') {
            for (let x = 0; x < this.rozmiar; x++) {
                const kolumna = [];
                for (let y = 0; y < this.rozmiar; y++) {
                    kolumna.push(sciana[x][y].wartosc);
                }
                
                // Naprawka: odwrócenie logiki dla góra/dół
                const nowaKolumna = this.przetworzLinie2048(kolumna, kierunek === 'down');
                
                if (JSON.stringify(kolumna) !== JSON.stringify(nowaKolumna)) {
                    for (let y = 0; y < this.rozmiar; y++) {
                        sciana[x][y].aktualizujWartosc(nowaKolumna[y]);
                    }
                    ruchWykonany = true;
                }
            }
        } else if (kierunek === 'left' || kierunek === 'right') {
            for (let y = 0; y < this.rozmiar; y++) {
                const wiersz = [];
                for (let x = 0; x < this.rozmiar; x++) {
                    wiersz.push(sciana[x][y].wartosc);
                }
                
                const nowyWiersz = this.przetworzLinie2048(wiersz, kierunek === 'left');
                
                if (JSON.stringify(wiersz) !== JSON.stringify(nowyWiersz)) {
                    for (let x = 0; x < this.rozmiar; x++) {
                        sciana[x][y].aktualizujWartosc(nowyWiersz[x]);
                    }
                    ruchWykonany = true;
                }
            }
        }

        if (ruchWykonany) {
            setTimeout(() => {
                this.dodajLosowyKafelek();
            }, 300);
        }
        
        return ruchWykonany;
    }

    przetworzLinie2048(linia, doLewej) {
        // Usuń zera
        let bezZer = linia.filter(val => val !== 0);
        
        if (!doLewej) {
            bezZer.reverse();
        }
        
        // Połącz sąsiadujące jednakowe liczby
        const wynik = [];
        let i = 0;
        
        while (i < bezZer.length) {
            if (i < bezZer.length - 1 && bezZer[i] === bezZer[i + 1]) {
                const nowaWartosc = bezZer[i] * 2;
                wynik.push(nowaWartosc);
                this.wynik += nowaWartosc;
                i += 2;
            } else {
                wynik.push(bezZer[i]);
                i++;
            }
        }
        
        // Dodaj zera
        while (wynik.length < this.rozmiar) {
            wynik.push(0);
        }
        
        if (!doLewej) {
            wynik.reverse();
        }
        
        return wynik;
    }

    sprawdzCzy2048() {
        return Object.keys(this.sciany).some(nazwaSciana => {
            return this.sciany[nazwaSciana].some(kolumna => {
                return kolumna.some(kafelek => kafelek.wartosc === 2048);
            });
        });
    }

    sprawdzKoniecGry() {
        return this.policzPustePola() === 0 && !this.sprawdzMozliweRuchy();
    }

    sprawdzMozliweRuchy() {
        return Object.keys(this.sciany).some(nazwaSciana => {
            const sciana = this.sciany[nazwaSciana];
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    const wartosc = sciana[x][y].wartosc;
                    if (wartosc === 0) return true;
                    
                    const sasiedzi = [
                        [x+1, y], [x-1, y], [x, y+1], [x, y-1]
                    ];
                    
                    for (let [sx, sy] of sasiedzi) {
                        if (sx >= 0 && sx < this.rozmiar && sy >= 0 && sy < this.rozmiar) {
                            if (sciana[sx][sy].wartosc === wartosc) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        });
    }

    policzPustePola() {
        let puste = 0;
        Object.keys(this.sciany).forEach(nazwaSciana => {
            for (let x = 0; x < this.rozmiar; x++) {
                for (let y = 0; y < this.rozmiar; y++) {
                    if (this.sciany[nazwaSciana][x][y].wartosc === 0) {
                        puste++;
                    }
                }
            }
        });
        return puste;
    }

    obrocWarstwe(warstwa, kierunek) {
        // Prawdziwy obrót warstwy jak w kostce Rubika
        const wartosciDoObrotu = this.zbierzWartosciWarstwy(warstwa);
        const koloryDoObrotu = this.zbierzKoloryWarstwy(warstwa);
        const noweWartosci = this.obrocWartosciWarstwy(wartosciDoObrotu, kierunek);
        const noweKolory = this.obrocWartosciWarstwy(koloryDoObrotu, kierunek);
        this.ustawWartosciWarstwy(warstwa, noweWartosci, noweKolory);

        // Dodatkowo obróć samą ścianę górną/dolną jeśli obracamy te warstwy
        if (warstwa === 'top') {
            this.obrocSciane('top', kierunek);
        } else if (warstwa === 'bottom') {
            this.obrocSciane('bottom', kierunek);
        }

        // Zaktualizuj podgląd natychmiast
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }

    zbierzWartosciWarstwy(warstwa) {
        const wartosci = {};

        switch(warstwa) {
            case 'top': // Górna warstwa (y = 1)
                wartosci.front = [this.sciany.front[0][2].wartosc, this.sciany.front[1][2].wartosc, this.sciany.front[2][2].wartosc];
                wartosci.right = [this.sciany.right[0][2].wartosc, this.sciany.right[1][2].wartosc, this.sciany.right[2][2].wartosc];
                wartosci.back = [this.sciany.back[0][2].wartosc, this.sciany.back[1][2].wartosc, this.sciany.back[2][2].wartosc];
                wartosci.left = [this.sciany.left[0][2].wartosc, this.sciany.left[1][2].wartosc, this.sciany.left[2][2].wartosc];
                break;
            case 'middle': // Środkowa warstwa (y = 0)
                wartosci.front = [this.sciany.front[0][1].wartosc, this.sciany.front[1][1].wartosc, this.sciany.front[2][1].wartosc];
                wartosci.right = [this.sciany.right[0][1].wartosc, this.sciany.right[1][1].wartosc, this.sciany.right[2][1].wartosc];
                wartosci.back = [this.sciany.back[0][1].wartosc, this.sciany.back[1][1].wartosc, this.sciany.back[2][1].wartosc];
                wartosci.left = [this.sciany.left[0][1].wartosc, this.sciany.left[1][1].wartosc, this.sciany.left[2][1].wartosc];
                break;
            case 'bottom': // Dolna warstwa (y = -1)
                wartosci.front = [this.sciany.front[0][0].wartosc, this.sciany.front[1][0].wartosc, this.sciany.front[2][0].wartosc];
                wartosci.right = [this.sciany.right[0][0].wartosc, this.sciany.right[1][0].wartosc, this.sciany.right[2][0].wartosc];
                wartosci.back = [this.sciany.back[0][0].wartosc, this.sciany.back[1][0].wartosc, this.sciany.back[2][0].wartosc];
                wartosci.left = [this.sciany.left[0][0].wartosc, this.sciany.left[1][0].wartosc, this.sciany.left[2][0].wartosc];
                break;
        }

        return wartosci;
    }

    zbierzKoloryWarstwy(warstwa) {
        const kolory = {};

        switch(warstwa) {
            case 'top': // Górna warstwa (y = 1)
                kolory.front = [this.sciany.front[0][2].kolorSciany, this.sciany.front[1][2].kolorSciany, this.sciany.front[2][2].kolorSciany];
                kolory.right = [this.sciany.right[0][2].kolorSciany, this.sciany.right[1][2].kolorSciany, this.sciany.right[2][2].kolorSciany];
                kolory.back = [this.sciany.back[0][2].kolorSciany, this.sciany.back[1][2].kolorSciany, this.sciany.back[2][2].kolorSciany];
                kolory.left = [this.sciany.left[0][2].kolorSciany, this.sciany.left[1][2].kolorSciany, this.sciany.left[2][2].kolorSciany];
                break;
            case 'middle': // Środkowa warstwa (y = 0)
                kolory.front = [this.sciany.front[0][1].kolorSciany, this.sciany.front[1][1].kolorSciany, this.sciany.front[2][1].kolorSciany];
                kolory.right = [this.sciany.right[0][1].kolorSciany, this.sciany.right[1][1].kolorSciany, this.sciany.right[2][1].kolorSciany];
                kolory.back = [this.sciany.back[0][1].kolorSciany, this.sciany.back[1][1].kolorSciany, this.sciany.back[2][1].kolorSciany];
                kolory.left = [this.sciany.left[0][1].kolorSciany, this.sciany.left[1][1].kolorSciany, this.sciany.left[2][1].kolorSciany];
                break;
            case 'bottom': // Dolna warstwa (y = -1)
                kolory.front = [this.sciany.front[0][0].kolorSciany, this.sciany.front[1][0].kolorSciany, this.sciany.front[2][0].kolorSciany];
                kolory.right = [this.sciany.right[0][0].kolorSciany, this.sciany.right[1][0].kolorSciany, this.sciany.right[2][0].kolorSciany];
                kolory.back = [this.sciany.back[0][0].kolorSciany, this.sciany.back[1][0].kolorSciany, this.sciany.back[2][0].kolorSciany];
                kolory.left = [this.sciany.left[0][0].kolorSciany, this.sciany.left[1][0].kolorSciany, this.sciany.left[2][0].kolorSciany];
                break;
        }

        return kolory;
    }

    obrocWartosciWarstwy(wartosci, kierunek) {
        const sciany = ['front', 'right', 'back', 'left'];
        const noweWartosci = {};
        
        for (let i = 0; i < sciany.length; i++) {
            const obecnaSciana = sciany[i];
            const nastepnaSciana = sciany[(i + kierunek + sciany.length) % sciany.length];
            noweWartosci[obecnaSciana] = wartosci[nastepnaSciana];
        }
        
        return noweWartosci;
    }

    ustawWartosciWarstwy(warstwa, wartosci, koloryWarstwy) {
        switch(warstwa) {
            case 'top':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][2].aktualizujWartosc(wartosci.front[x], koloryWarstwy.front[x]);
                    this.sciany.right[x][2].aktualizujWartosc(wartosci.right[x], koloryWarstwy.right[x]);
                    this.sciany.back[x][2].aktualizujWartosc(wartosci.back[x], koloryWarstwy.back[x]);
                    this.sciany.left[x][2].aktualizujWartosc(wartosci.left[x], koloryWarstwy.left[x]);
                }
                break;
            case 'middle':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][1].aktualizujWartosc(wartosci.front[x], koloryWarstwy.front[x]);
                    this.sciany.right[x][1].aktualizujWartosc(wartosci.right[x], koloryWarstwy.right[x]);
                    this.sciany.back[x][1].aktualizujWartosc(wartosci.back[x], koloryWarstwy.back[x]);
                    this.sciany.left[x][1].aktualizujWartosc(wartosci.left[x], koloryWarstwy.left[x]);
                }
                break;
            case 'bottom':
                for (let x = 0; x < 3; x++) {
                    this.sciany.front[x][0].aktualizujWartosc(wartosci.front[x], koloryWarstwy.front[x]);
                    this.sciany.right[x][0].aktualizujWartosc(wartosci.right[x], koloryWarstwy.right[x]);
                    this.sciany.back[x][0].aktualizujWartosc(wartosci.back[x], koloryWarstwy.back[x]);
                    this.sciany.left[x][0].aktualizujWartosc(wartosci.left[x], koloryWarstwy.left[x]);
                }
                break;
        }
    }

    // Nowa metoda do obracania samej ściany (top/bottom)
    obrocSciane(sciana, kierunek) {
        const stareWartosci = [];
        const stareKolory = [];

        // Zbierz wartości i kolory w kolejności macierzy 3x3
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                stareWartosci[x * 3 + y] = this.sciany[sciana][x][y].wartosc;
                stareKolory[x * 3 + y] = this.sciany[sciana][x][y].kolorSciany;
            }
        }

        // Obrót macierzy 3x3
        const noweWartosci = [];
        const noweKolory = [];
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                if (kierunek === 1) { // Obrót w prawo (zgodnie z ruchem wskazówek zegara)
                    noweWartosci[x * 3 + y] = stareWartosci[(2 - y) * 3 + x];
                    noweKolory[x * 3 + y] = stareKolory[(2 - y) * 3 + x];
                } else { // Obrót w lewo (przeciwnie do ruchu wskazówek zegara)
                    noweWartosci[x * 3 + y] = stareWartosci[y * 3 + (2 - x)];
                    noweKolory[x * 3 + y] = stareKolory[y * 3 + (2 - x)];
                }
            }
        }

        // Ustaw nowe wartości i kolory
        for (let x = 0; x < 3; x++) {
            for (let y = 0; y < 3; y++) {
                this.sciany[sciana][x][y].aktualizujWartosc(noweWartosci[x * 3 + y], noweKolory[x * 3 + y]);
            }
        }
    }

    // Metoda do obracania warstw pionowych (left/right/center)
    obrocWarstweVertical(warstwa, kierunek) {
        const wartosciDoObrotu = this.zbierzWartosciWarstwaPionowa(warstwa);
        const koloryDoObrotu = this.zbierzKoloryWarstwaPionowa(warstwa);
        const noweWartosci = this.obrocWartosciWarstwaPionowa(wartosciDoObrotu, kierunek);
        const noweKolory = this.obrocWartosciWarstwaPionowa(koloryDoObrotu, kierunek);
        this.ustawWartosciWarstwaPionowa(warstwa, noweWartosci, noweKolory);

        // Dodatowo obróć samą ścianę left/right jeśli obracamy te warstwy
        if (warstwa === 'left') {
            this.obrocSciane('left', kierunek);
        } else if (warstwa === 'right') {
            this.obrocSciane('right', kierunek);
        }

        // Zaktualizuj podgląd natychmiast
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }

    zbierzWartosciWarstwaPionowa(warstwa) {
        const wartosci = {};

        switch(warstwa) {
            case 'left': // Lewa warstwa (x = 0)
                wartosci.front = [this.sciany.front[0][0].wartosc, this.sciany.front[0][1].wartosc, this.sciany.front[0][2].wartosc];
                wartosci.top = [this.sciany.top[0][0].wartosc, this.sciany.top[0][1].wartosc, this.sciany.top[0][2].wartosc];
                wartosci.back = [this.sciany.back[2][0].wartosc, this.sciany.back[2][1].wartosc, this.sciany.back[2][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[0][2].wartosc, this.sciany.bottom[0][1].wartosc, this.sciany.bottom[0][0].wartosc];
                break;
            case 'center': // Środkowa warstwa pionowa (x = 1)
                wartosci.front = [this.sciany.front[1][0].wartosc, this.sciany.front[1][1].wartosc, this.sciany.front[1][2].wartosc];
                wartosci.top = [this.sciany.top[1][0].wartosc, this.sciany.top[1][1].wartosc, this.sciany.top[1][2].wartosc];
                wartosci.back = [this.sciany.back[1][0].wartosc, this.sciany.back[1][1].wartosc, this.sciany.back[1][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[1][2].wartosc, this.sciany.bottom[1][1].wartosc, this.sciany.bottom[1][0].wartosc];
                break;
            case 'right': // Prawa warstwa (x = 2)
                wartosci.front = [this.sciany.front[2][0].wartosc, this.sciany.front[2][1].wartosc, this.sciany.front[2][2].wartosc];
                wartosci.top = [this.sciany.top[2][0].wartosc, this.sciany.top[2][1].wartosc, this.sciany.top[2][2].wartosc];
                wartosci.back = [this.sciany.back[0][0].wartosc, this.sciany.back[0][1].wartosc, this.sciany.back[0][2].wartosc];
                wartosci.bottom = [this.sciany.bottom[2][2].wartosc, this.sciany.bottom[2][1].wartosc, this.sciany.bottom[2][0].wartosc];
                break;
        }

        return wartosci;
    }

    obrocWartosciWarstwaPionowa(wartosci, kierunek) {
        const sciany = ['front', 'top', 'back', 'bottom'];
        const noweWartosci = {};

        for (let i = 0; i < sciany.length; i++) {
            const obecnaSciana = sciany[i];
            const nastepnaSciana = sciany[(i + kierunek + sciany.length) % sciany.length];
            noweWartosci[obecnaSciana] = wartosci[nastepnaSciana];
        }

        return noweWartosci;
    }

    zbierzKoloryWarstwaPionowa(warstwa) {
        const kolory = {};

        switch(warstwa) {
            case 'left': // Lewa warstwa (x = 0)
                kolory.front = [this.sciany.front[0][0].kolorSciany, this.sciany.front[0][1].kolorSciany, this.sciany.front[0][2].kolorSciany];
                kolory.top = [this.sciany.top[0][0].kolorSciany, this.sciany.top[0][1].kolorSciany, this.sciany.top[0][2].kolorSciany];
                kolory.back = [this.sciany.back[2][0].kolorSciany, this.sciany.back[2][1].kolorSciany, this.sciany.back[2][2].kolorSciany];
                kolory.bottom = [this.sciany.bottom[0][2].kolorSciany, this.sciany.bottom[0][1].kolorSciany, this.sciany.bottom[0][0].kolorSciany];
                break;
            case 'center': // Środkowa warstwa pionowa (x = 1)
                kolory.front = [this.sciany.front[1][0].kolorSciany, this.sciany.front[1][1].kolorSciany, this.sciany.front[1][2].kolorSciany];
                kolory.top = [this.sciany.top[1][0].kolorSciany, this.sciany.top[1][1].kolorSciany, this.sciany.top[1][2].kolorSciany];
                kolory.back = [this.sciany.back[1][0].kolorSciany, this.sciany.back[1][1].kolorSciany, this.sciany.back[1][2].kolorSciany];
                kolory.bottom = [this.sciany.bottom[1][2].kolorSciany, this.sciany.bottom[1][1].kolorSciany, this.sciany.bottom[1][0].kolorSciany];
                break;
            case 'right': // Prawa warstwa (x = 2)
                kolory.front = [this.sciany.front[2][0].kolorSciany, this.sciany.front[2][1].kolorSciany, this.sciany.front[2][2].kolorSciany];
                kolory.top = [this.sciany.top[2][0].kolorSciany, this.sciany.top[2][1].kolorSciany, this.sciany.top[2][2].kolorSciany];
                kolory.back = [this.sciany.back[0][0].kolorSciany, this.sciany.back[0][1].kolorSciany, this.sciany.back[0][2].kolorSciany];
                kolory.bottom = [this.sciany.bottom[2][2].kolorSciany, this.sciany.bottom[2][1].kolorSciany, this.sciany.bottom[2][0].kolorSciany];
                break;
        }

        return kolory;
    }

    ustawWartosciWarstwaPionowa(warstwa, wartosci, kolory) {
        switch(warstwa) {
            case 'left':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[0][y].aktualizujWartosc(wartosci.front[y], kolory.front[y]);
                    this.sciany.top[0][y].aktualizujWartosc(wartosci.top[y], kolory.top[y]);
                    this.sciany.back[2][y].aktualizujWartosc(wartosci.back[y], kolory.back[y]);
                    this.sciany.bottom[0][2-y].aktualizujWartosc(wartosci.bottom[y], kolory.bottom[y]);
                }
                break;
            case 'center':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[1][y].aktualizujWartosc(wartosci.front[y], kolory.front[y]);
                    this.sciany.top[1][y].aktualizujWartosc(wartosci.top[y], kolory.top[y]);
                    this.sciany.back[1][y].aktualizujWartosc(wartosci.back[y], kolory.back[y]);
                    this.sciany.bottom[1][2-y].aktualizujWartosc(wartosci.bottom[y], kolory.bottom[y]);
                }
                break;
            case 'right':
                for (let y = 0; y < 3; y++) {
                    this.sciany.front[2][y].aktualizujWartosc(wartosci.front[y], kolory.front[y]);
                    this.sciany.top[2][y].aktualizujWartosc(wartosci.top[y], kolory.top[y]);
                    this.sciany.back[0][y].aktualizujWartosc(wartosci.back[y], kolory.back[y]);
                    this.sciany.bottom[2][2-y].aktualizujWartosc(wartosci.bottom[y], kolory.bottom[y]);
                }
                break;
        }
    }

    // Metody do obracania kolorów ścian
    obrocKoloryScianWarstwa(warstwa, kierunek) {
        const scianyDoObrotu = ['front', 'right', 'back', 'left'];
        const stareKolory = {};

        // Zbierz aktualne kolory
        scianyDoObrotu.forEach(sciana => {
            stareKolory[sciana] = this.koloryScian[sciana];
        });

        // Obróć kolory
        for (let i = 0; i < scianyDoObrotu.length; i++) {
            const obecnaSciana = scianyDoObrotu[i];
            const nastepnaSciana = scianyDoObrotu[(i + kierunek + scianyDoObrotu.length) % scianyDoObrotu.length];
            this.koloryScian[obecnaSciana] = stareKolory[nastepnaSciana];
        }
    }

    obrocKoloryScianWarstwaPionowa(warstwa, kierunek) {
        const scianyDoObrotu = ['front', 'top', 'back', 'bottom'];
        const stareKolory = {};

        // Zbierz aktualne kolory
        scianyDoObrotu.forEach(sciana => {
            stareKolory[sciana] = this.koloryScian[sciana];
        });

        // Obróć kolory
        for (let i = 0; i < scianyDoObrotu.length; i++) {
            const obecnaSciana = scianyDoObrotu[i];
            const nastepnaSciana = scianyDoObrotu[(i + kierunek + scianyDoObrotu.length) % scianyDoObrotu.length];
            this.koloryScian[obecnaSciana] = stareKolory[nastepnaSciana];
        }
    }

    aktualizujKoloryScian3D() {
        // Aktualizuj kolory ścian w 3D na podstawie aktualnych kolorów
        Object.keys(this.scianySześcianu).forEach(sciana => {
            if (this.scianySześcianu[sciana] && this.koloryScian[sciana]) {
                this.scianySześcianu[sciana].material.color.setHex(
                    parseInt(this.koloryScian[sciana].replace('#', '0x'))
                );
            }
        });
    }

    // Metody do obracania wierszy i kolumn na aktywnej ścianie
    obrocWiersz(wiersz, kierunek) {
        const sciana = this.aktywnaSciana;
        const stareWartosci = [];
        const stareKolory = [];

        // Zbierz wartości i kolory z wiersza
        for (let x = 0; x < 3; x++) {
            stareWartosci[x] = this.sciany[sciana][x][wiersz].wartosc;
            stareKolory[x] = this.sciany[sciana][x][wiersz].kolorSciany;
        }

        // Obróć wartości i kolory
        const noweWartosci = [];
        const noweKolory = [];
        for (let x = 0; x < 3; x++) {
            if (kierunek === 1) { // W prawo
                noweWartosci[x] = stareWartosci[(x + 2) % 3];
                noweKolory[x] = stareKolory[(x + 2) % 3];
            } else { // W lewo
                noweWartosci[x] = stareWartosci[(x + 1) % 3];
                noweKolory[x] = stareKolory[(x + 1) % 3];
            }
        }

        // Ustaw nowe wartości i kolory
        for (let x = 0; x < 3; x++) {
            this.sciany[sciana][x][wiersz].aktualizujWartosc(noweWartosci[x], noweKolory[x]);
        }

        // Zaktualizuj podgląd
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }

    obrocKolumne(kolumna, kierunek) {
        const sciana = this.aktywnaSciana;
        const stareWartosci = [];
        const stareKolory = [];

        // Zbierz wartości i kolory z kolumny
        for (let y = 0; y < 3; y++) {
            stareWartosci[y] = this.sciany[sciana][kolumna][y].wartosc;
            stareKolory[y] = this.sciany[sciana][kolumna][y].kolorSciany;
        }

        // Obróć wartości i kolory
        const noweWartosci = [];
        const noweKolory = [];
        for (let y = 0; y < 3; y++) {
            if (kierunek === 1) { // W dół
                noweWartosci[y] = stareWartosci[(y + 2) % 3];
                noweKolory[y] = stareKolory[(y + 2) % 3];
            } else { // W górę
                noweWartosci[y] = stareWartosci[(y + 1) % 3];
                noweKolory[y] = stareKolory[(y + 1) % 3];
            }
        }

        // Ustaw nowe wartości i kolory
        for (let y = 0; y < 3; y++) {
            this.sciany[sciana][kolumna][y].aktualizujWartosc(noweWartosci[y], noweKolory[y]);
        }

        // Zaktualizuj podgląd
        if (window.gra) {
            window.gra.aktualizujPodgladScian();
        }
    }
}

// Klasa MenedżerGry
class MenedzerGry {
    constructor() {
        this.kostka = new Kostka3D();
        this.konfiguracjaSterowania();
        this.stworzUI();
        this.stworzPodgladScian();
        this.aktualizujUI();
        this.stanGry = 'gra';
        this.obracanie = false;
        this.ostatniaPozycjaMyszy = { x: 0, y: 0 };
    }

    konfiguracjaSterowania() {
        // Hover dla aktywacji ściany - tylko gdy nie obracamy
        document.addEventListener('mousemove', (event) => {
            if (isDragging) return; // Nie zmieniaj aktywnej ściany podczas obracania

            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(this.kostka.grupaKostki.children);

            if (intersects.length > 0) {
                const obj = intersects[0].object;
                if (obj.userData.typ === 'sciana') {
                    this.kostka.podswietlSciane(obj.userData.sciana);
                    this.aktualizujUI();
                    this.aktualizujPodgladScian();
                }
            }
        });

        // Klawiatura
        document.addEventListener('keydown', (event) => {
            if (this.stanGry !== 'gra') return;
            
            switch(event.code) {
                case 'ArrowUp':
                    this.kostka.przesunWierszLubKolumne('up');
                    break;
                case 'ArrowDown':
                    this.kostka.przesunWierszLubKolumne('down');
                    break;
                case 'ArrowLeft':
                    this.kostka.przesunWierszLubKolumne('left');
                    break;
                case 'ArrowRight':
                    this.kostka.przesunWierszLubKolumne('right');
                    break;
                case 'Space':
                    event.preventDefault();
                    this.kostka.dodajLosowyKafelek();
                    break;
                case 'KeyR':
                    this.nowaGra();
                    break;
                // Obrót warstw kostki Rubika
                case 'KeyQ':
                    this.kostka.obrocWarstwe('top', 1);
                    break;
                case 'KeyE':
                    this.kostka.obrocWarstwe('top', -1);
                    break;
                case 'KeyA':
                    this.kostka.obrocWarstweVertical('left', 1);
                    break;
                case 'KeyD':
                    this.kostka.obrocWarstweVertical('right', 1);
                    break;
                case 'KeyW':
                    this.kostka.obrocWarstwe('middle', 1);
                    break;
                case 'KeyS':
                    this.kostka.obrocWarstwe('bottom', 1);
                    break;
                case 'KeyZ':
                    this.kostka.obrocWarstweVertical('center', 1);
                    break;
                // Obrót wierszy na aktywnej ścianie
                case 'Digit1':
                    this.kostka.obrocWiersz(0, 1);
                    break;
                case 'Digit2':
                    this.kostka.obrocWiersz(1, 1);
                    break;
                case 'Digit3':
                    this.kostka.obrocWiersz(2, 1);
                    break;
                // Obrót kolumn na aktywnej ścianie
                case 'Digit4':
                    this.kostka.obrocKolumne(0, 1);
                    break;
                case 'Digit5':
                    this.kostka.obrocKolumne(1, 1);
                    break;
                case 'Digit6':
                    this.kostka.obrocKolumne(2, 1);
                    break;
            }
            
            this.sprawdzStanGry();
            this.aktualizujUI();
            this.aktualizujPodgladScian();
        });

        // Obracanie kostki myszą - poprawione sterowanie
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };

        document.addEventListener('mousedown', (event) => {
            if (event.button === 0) {
                isDragging = true;
                previousMousePosition.x = event.clientX;
                previousMousePosition.y = event.clientY;
                document.body.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            document.body.style.cursor = 'default';
        });

        document.addEventListener('mousemove', (event) => {
            if (!isDragging) return;

            const deltaX = event.clientX - previousMousePosition.x;
            const deltaY = event.clientY - previousMousePosition.y;

            // Poprawione sterowanie - bardziej intuicyjne i płynne
            this.kostka.grupaKostki.rotation.y += deltaX * 0.008;
            this.kostka.grupaKostki.rotation.x -= deltaY * 0.008; // Odwrócone dla bardziej naturalnego sterowania

            // Ograniczenie obrotu w pionie
            this.kostka.grupaKostki.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x));

            previousMousePosition.x = event.clientX;
            previousMousePosition.y = event.clientY;

            // Automatycznie aktualizuj aktywną ścianę podczas obracania
            this.aktualizujAktywnaSciane();
        });

        // Touch events - poprawione
        document.addEventListener('touchstart', (event) => {
            if (event.touches.length === 1) {
                isDragging = true;
                previousMousePosition.x = event.touches[0].clientX;
                previousMousePosition.y = event.touches[0].clientY;
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });

        document.addEventListener('touchmove', (event) => {
            if (!isDragging || event.touches.length !== 1) return;

            event.preventDefault();
            const deltaX = event.touches[0].clientX - previousMousePosition.x;
            const deltaY = event.touches[0].clientY - previousMousePosition.y;

            this.kostka.grupaKostki.rotation.y += deltaX * 0.008;
            this.kostka.grupaKostki.rotation.x -= deltaY * 0.008; // Odwrócone dla bardziej naturalnego sterowania

            // Ograniczenie obrotu w pionie
            this.kostka.grupaKostki.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x));

            previousMousePosition.x = event.touches[0].clientX;
            previousMousePosition.y = event.touches[0].clientY;

            // Automatycznie aktualizuj aktywną ścianę podczas obracania
            this.aktualizujAktywnaSciane();
        });

        document.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    stworzPodgladScian() {
        const podglad = document.createElement('div');
        podglad.id = 'podgladScian';
        podglad.innerHTML = `
            <h3>Podgląd wszystkich ścian</h3>
            <div class="sciany-grid">
                ${['front', 'back', 'left', 'right', 'top', 'bottom'].map(sciana => `
                    <div class="sciana-podglad" data-sciana="${sciana}">
                        <div class="nazwa-sciany">${sciana.toUpperCase()}</div>
                        <canvas id="canvas-${sciana}" width="300" height="300"></canvas>
                    </div>
                `).join('')}
            </div>
        `;
        
        document.body.appendChild(podglad);

        // Dodaj event listenery dla kliknięć na ściany w podglądzie
        document.querySelectorAll('.sciana-podglad').forEach(element => {
            element.addEventListener('click', () => {
                const sciana = element.getAttribute('data-sciana');
                this.kostka.podswietlSciane(sciana);
                this.aktualizujUI();
                this.aktualizujPodgladScian();
            });
        });

        this.aktualizujPodgladScian();
    }

    aktualizujPodgladScian() {
        // Bazowe kolory ścian dla tła
        const koloryScian = {
            'front': '#dc3545',   'back': '#fd7e14',    'left': '#0d6efd',
            'right': '#198754',   'top': '#ffc107',     'bottom': '#6f42c1'
        };

        Object.keys(this.kostka.sciany).forEach(sciana => {
            const canvas = document.getElementById(`canvas-${sciana}`);
            const scianaPodglad = canvas?.parentElement;

            if (canvas && scianaPodglad) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Podświetl aktywną ścianę
                if (sciana === this.kostka.aktywnaSciana) {
                    scianaPodglad.classList.add('active');
                } else {
                    scianaPodglad.classList.remove('active');
                }

                // Tło ściany z gradientem
                const gradient = ctx.createLinearGradient(0, 0, 300, 300);
                gradient.addColorStop(0, koloryScian[sciana]);
                gradient.addColorStop(1, this.darkenColor(koloryScian[sciana], 0.2));
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 300, 300);

                // Rysuj siatkę 3x3
                for (let x = 0; x < 3; x++) {
                    for (let y = 0; y < 3; y++) {
                        const wartosc = this.kostka.sciany[sciana][x][y].wartosc;
                        const rozmiarKomorki = 100;
                        const startX = x * rozmiarKomorki;
                        const startY = y * rozmiarKomorki;

                        // Użyj koloru kafelka (który może się różnić od koloru ściany po obrotach)
                        const kolorKafelka = this.kostka.sciany[sciana][x][y].kolorSciany;

                        if (wartosc > 0) {
                            const kolor = this.pobierzKolorDlaWartosci(wartosc);

                            // Gradient dla kafelka
                            const kafelekGradient = ctx.createRadialGradient(
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, 0,
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, rozmiarKomorki/2
                            );
                            kafelekGradient.addColorStop(0, kolor);
                            kafelekGradient.addColorStop(1, this.darkenColor(kolor, 0.3));

                            ctx.fillStyle = kafelekGradient;
                            ctx.fillRect(startX + 8, startY + 8, rozmiarKomorki - 16, rozmiarKomorki - 16);

                            // Określ kolor tekstu na podstawie wartości
                            const kolorTekstu = this.pobierzKolorTekstuDlaWartosci(wartosc);

                            // Cień tekstu dla lepszej czytelności
                            ctx.fillStyle = kolorTekstu === '#ffffff' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.5)';
                            ctx.font = 'bold 32px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(wartosc.toString(), startX + rozmiarKomorki/2 + 1, startY + rozmiarKomorki/2 + 1);

                            // Główny tekst z odpowiednim kolorem
                            ctx.fillStyle = kolorTekstu;
                            ctx.strokeStyle = kolorTekstu === '#ffffff' ? '#000000' : '#ffffff';
                            ctx.lineWidth = 2;
                            ctx.strokeText(wartosc.toString(), startX + rozmiarKomorki/2, startY + rozmiarKomorki/2);
                            ctx.fillText(wartosc.toString(), startX + rozmiarKomorki/2, startY + rozmiarKomorki/2);
                        } else {
                            // Puste pole - użyj koloru kafelka (który może się różnić od bazowego koloru ściany)
                            const kafelekGradient = ctx.createRadialGradient(
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, 0,
                                startX + rozmiarKomorki/2, startY + rozmiarKomorki/2, rozmiarKomorki/2
                            );
                            kafelekGradient.addColorStop(0, kolorKafelka);
                            kafelekGradient.addColorStop(1, this.darkenColor(kolorKafelka, 0.3));

                            ctx.fillStyle = kafelekGradient;
                            ctx.fillRect(startX + 8, startY + 8, rozmiarKomorki - 16, rozmiarKomorki - 16);
                        }

                        // Ramka komórki
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(startX + 2, startY + 2, rozmiarKomorki - 4, rozmiarKomorki - 4);
                    }
                }

                // Podświetl aktywną ścianę
                if (sciana === this.kostka.aktywnaSciana) {
                    ctx.strokeStyle = '#ffff00';
                    ctx.lineWidth = 6;
                    ctx.strokeRect(3, 3, 294, 294);
                }
            }
        });
    }

    // Pomocnicza funkcja do ciemnienia kolorów
    darkenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
        return `rgb(${r}, ${g}, ${b})`;
    }

    pobierzKolorDlaWartosci(wartosc) {
        const kolory = {
            2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
            32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
            512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
        };
        return kolory[wartosc] || '#3c3a32';
    }

    pobierzKolorTekstuDlaWartosci(wartosc) {
        // Biały tekst dla ciemniejszych tła, czarny dla jasnych
        const ciemneKolory = [2, 4];
        return ciemneKolory.includes(wartosc) ? '#000000' : '#ffffff';
    }

    sprawdzStanGry() {
        if (this.kostka.sprawdzCzy2048() && this.stanGry === 'gra') {
            this.stanGry = 'wygrana';
            this.pokazKomunikat('🎉 Gratulacje! Osiągnąłeś 2048!', '#27ae60');
        } else if (this.kostka.sprawdzKoniecGry()) {
            this.stanGry = 'przegrana';
            this.pokazKomunikat('💀 Koniec gry! Brak możliwych ruchów.', '#e74c3c');
        }
    }

    pokazKomunikat(tekst, kolor) {
        const komunikat = document.createElement('div');
        komunikat.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: ${kolor};
            color: white;
            padding: 30px 50px;
            border-radius: 20px;
            font-size: 28px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        `;
        komunikat.textContent = tekst;
        
        document.body.appendChild(komunikat);
        
        setTimeout(() => {
            if (document.body.contains(komunikat)) {
                document.body.removeChild(komunikat);
            }
        }, 4000);
    }

    stworzUI() {
        const ui = document.createElement('div');
        ui.id = 'gameUI';
        ui.innerHTML = `
            <div class="score">� Wynik: <span id="wynik">0</span></div>
            <div class="score">📦 Puste pola: <span id="pustePola">54</span></div>
            <div class="active-face">🎯 Aktywna ściana: <span id="aktywnaSciana">FRONT</span></div>
            
            <!-- Strzałki sterowania kostką -->
            <div class="kostka-controls">
                <div class="rotation-controls">
                    <button id="rotateUp" class="control-btn">⬆️</button>
                    <div class="horizontal-controls">
                        <button id="rotateLeft" class="control-btn">⬅️</button>
                        <span class="control-label">Obrót kostki</span>
                        <button id="rotateRight" class="control-btn">➡️</button>
                    </div>
                    <button id="rotateDown" class="control-btn">⬇️</button>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <strong>🎮 Sterowanie gry:</strong>
                    <div>🖱️ Najedź myszą - wybór ściany</div>
                    <div>⬅️➡️⬆️⬇️ Strzałki - przesuwanie cyfr</div>
                    <div>🖱️ Przeciągnij - obrót kostki</div>
                </div>
                
                <div class="control-group">
                    <strong>🔄 Obrót warstw (jak Rubik):</strong>
                    <div>Q/E - górna warstwa</div>
                    <div>A/D - lewa/prawa warstwa</div>
                    <div>W/S - środkowa/dolna warstwa</div>
                    <div>Z - środkowa warstwa pionowa</div>
                </div>

                <div class="control-group">
                    <strong>🎯 Obrót na aktywnej ścianie:</strong>
                    <div>1/2/3 - obróć wiersze</div>
                    <div>4/5/6 - obróć kolumny</div>
                </div>
                
                <div class="control-group">
                    <div>⭐ Spacja - dodaj kafelek</div>
                    <div>🔄 R - nowa gra</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(ui);
        
        // Dodaj event listenery dla przycisków
        this.dodajEventListeneryPrzyciskow();
    }

    dodajEventListeneryPrzyciskow() {
        document.getElementById('rotateLeft').addEventListener('click', () => {
            this.animujObrot('y', -Math.PI / 4);
        });

        document.getElementById('rotateRight').addEventListener('click', () => {
            this.animujObrot('y', Math.PI / 4);
        });

        document.getElementById('rotateUp').addEventListener('click', () => {
            const nowyKat = Math.max(-Math.PI/2, this.kostka.grupaKostki.rotation.x - Math.PI / 4);
            this.animujObrot('x', nowyKat - this.kostka.grupaKostki.rotation.x);
        });

        document.getElementById('rotateDown').addEventListener('click', () => {
            const nowyKat = Math.min(Math.PI/2, this.kostka.grupaKostki.rotation.x + Math.PI / 4);
            this.animujObrot('x', nowyKat - this.kostka.grupaKostki.rotation.x);
        });
    }

    // Płynna animacja obrotu
    animujObrot(os, deltaKat) {
        const startTime = Date.now();
        const duration = 300; // 300ms animacji
        const startRotation = this.kostka.grupaKostki.rotation[os];
        const targetRotation = startRotation + deltaKat;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function dla płynniejszej animacji
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            this.kostka.grupaKostki.rotation[os] = startRotation + (deltaKat * easeProgress);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.kostka.grupaKostki.rotation[os] = targetRotation;
                this.aktualizujAktywnaSciane();
            }

            // Aktualizuj podczas animacji też
            this.aktualizujAktywnaSciane();
        };

        animate();
    }

    aktualizujAktywnaSciane() {
        // Automatycznie wybierz najbardziej widoczną ścianę na podstawie rotacji
        const rotY = this.kostka.grupaKostki.rotation.y;
        const rotX = this.kostka.grupaKostki.rotation.x;

        let sciana = 'front';

        // Sprawdź czy patrzymy głównie z góry lub z dołu (większy próg)
        if (rotX < -Math.PI/3) {
            sciana = 'top';
        } else if (rotX > Math.PI/3) {
            sciana = 'bottom';
        } else {
            // Normalizuj rotację Y do zakresu 0-2π
            let normalizedY = rotY % (2 * Math.PI);
            if (normalizedY < 0) normalizedY += 2 * Math.PI;

            // Określ ścianę na podstawie kąta obrotu z lepszymi progami
            if (normalizedY >= 7*Math.PI/4 || normalizedY < Math.PI/4) {
                sciana = 'front';
            } else if (normalizedY >= Math.PI/4 && normalizedY < 3*Math.PI/4) {
                sciana = 'right';
            } else if (normalizedY >= 3*Math.PI/4 && normalizedY < 5*Math.PI/4) {
                sciana = 'back';
            } else if (normalizedY >= 5*Math.PI/4 && normalizedY < 7*Math.PI/4) {
                sciana = 'left';
            }
        }

        // Tylko zmień ścianę jeśli rzeczywiście się zmieniła
        if (sciana !== this.kostka.aktywnaSciana) {
            this.kostka.podswietlSciane(sciana);
            this.aktualizujUI();
            this.aktualizujPodgladScian();
        }
    }

    aktualizujUI() {
        document.getElementById('wynik').textContent = this.kostka.wynik.toLocaleString();
        document.getElementById('pustePola').textContent = this.kostka.policzPustePola();
        document.getElementById('aktywnaSciana').textContent = this.kostka.aktywnaSciana.toUpperCase();
    }

    nowaGra() {
        scene.remove(this.kostka.grupaKostki);
        this.kostka = new Kostka3D();
        this.stanGry = 'gra';
        this.aktualizujUI();
        this.aktualizujPodgladScian();
    }
}

// Inicjalizacja gry
const gra = new MenedzerGry();
window.gra = gra; // Dodaj do window dla dostępu globalnego

// Pozycja kamery - dostosowana do większej kostki
camera.position.set(18, 14, 18); // Jeszcze dalej dla większej kostki
camera.lookAt(0, 0, 0);

// Pętla animacji
function animuj() {
    requestAnimationFrame(animuj);
    renderer.render(scene, camera);
}
animuj();

