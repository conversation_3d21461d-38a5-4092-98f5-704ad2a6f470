<!DOCTYPE html>
<html>
<head>
    <title>Super prosty test</title>
    <style>
        body { margin: 0; background: #000; }
        #info { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; }
    </style>
</head>
<body>
    <div id="info">
        <h3>Test Three.js</h3>
        <div id="status">Ładowanie...</div>
    </div>

    <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
    <script>
        document.getElementById('status').textContent = 'Three.js załadowane: ' + (typeof THREE !== 'undefined');
        
        if (typeof THREE === 'undefined') {
            document.getElementById('status').textContent = 'BŁĄD: Three.js nie załadowane!';
        } else {
            try {
                // Scena
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer();
                renderer.setSize(window.innerWidth, window.innerHeight);
                document.body.appendChild(renderer.domElement);
                
                // Kostka
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                camera.position.z = 5;
                
                document.getElementById('status').textContent = 'Kostka utworzona, animacja...';
                
                // Animacja
                function animate() {
                    requestAnimationFrame(animate);
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    renderer.render(scene, camera);
                }
                animate();
                
            } catch (error) {
                document.getElementById('status').textContent = 'BŁĄD: ' + error.message;
                console.error(error);
            }
        }
    </script>
</body>
</html>
