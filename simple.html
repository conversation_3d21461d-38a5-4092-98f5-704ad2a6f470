<!DOCTYPE html>
<html lang="pl">
<head>
  <title>Prosta kostka 2048</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; }
    #info { position: absolute; top: 10px; left: 10px; color: white; font-family: Arial; z-index: 100; }
  </style>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
</head>
<body>
  <div id="info">Prosta kostka 2048 - strzałki do ruchu, Q/E do obrotu warstw</div>
  <script>
    console.log('Rozpoczynam prostą kostkę...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const light = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(light);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Kolory ścian
    const colors = {
      front: 0xff0000,  // czerwony
      back: 0x00ff00,   // zielony  
      left: 0x0000ff,   // niebieski
      right: 0xffff00,  // żółty
      top: 0xff00ff,    // magenta
      bottom: 0x00ffff  // cyan
    };
    
    // Tworzenie ścian kostki
    const faceSize = 3;
    const spacing = 1.1;
    const offset = 3;
    
    // Dane gry - prosta tablica 3x3 dla każdej ściany
    const gameData = {
      front: [[0,2,0],[0,0,0],[0,0,4]],
      back: [[0,0,0],[0,0,0],[0,0,0]],
      left: [[0,0,0],[0,0,0],[0,0,0]],
      right: [[0,0,0],[0,0,0],[0,0,0]],
      top: [[0,0,0],[0,0,0],[0,0,0]],
      bottom: [[0,0,0],[0,0,0],[0,0,0]]
    };
    
    let activeFace = 'front';
    const tiles = {};
    
    // Tworzenie kafelków
    function createTiles() {
      Object.keys(colors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const geometry = new THREE.PlaneGeometry(0.9, 0.9);
            const material = new THREE.MeshBasicMaterial({ 
              color: colors[face],
              transparent: true,
              opacity: gameData[face][x][y] > 0 ? 1.0 : 0.3
            });
            
            const tile = new THREE.Mesh(geometry, material);
            
            // Pozycjonowanie
            const posX = (x - 1) * spacing;
            const posY = (y - 1) * spacing;
            
            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, -posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, posY);
                tile.rotation.x = Math.PI/2;
                break;
            }
            
            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });
    }
    
    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            tiles[face][x][y].material.opacity = value > 0 ? 1.0 : 0.3;
            // Można dodać teksty z cyframi później
          }
        }
      });
    }
    
    // Sterowanie
    document.addEventListener('keydown', (event) => {
      console.log('Klawisz:', event.code);
      
      switch(event.code) {
        case 'ArrowLeft':
          // Przesuń w lewo na aktywnej ścianie
          console.log('Ruch w lewo');
          break;
        case 'ArrowRight':
          console.log('Ruch w prawo');
          break;
        case 'KeyQ':
          // Obróć górną warstwę
          console.log('Obrót górnej warstwy');
          break;
      }
    });
    
    // Pozycja kamery
    camera.position.set(8, 6, 8);
    camera.lookAt(0, 0, 0);
    
    // Inicjalizacja
    createTiles();
    updateTiles();
    
    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    
    console.log('Kostka utworzona, rozpoczynam animację');
    animate();
  </script>
</body>
</html>
