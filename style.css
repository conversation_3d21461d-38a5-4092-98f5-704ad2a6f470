body {
  margin: 0;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  font-family: 'Arial', sans-serif;
}

canvas {
  display: block;
}

#gameUI {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-size: 16px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 300px;
}

#podgladScian {
  position: absolute;
  top: 20px;
  right: 20px;
  color: white;
  font-size: 14px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.85);
  padding: 25px;
  border-radius: 15px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

#podgladScian h3 {
  margin: 0 0 15px 0;
  color: #3498db;
  text-align: center;
}

.sciany-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.sciana-podglad {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;
}

.sciana-podglad:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.nazwa-sciany {
  font-size: 12px;
  margin-bottom: 5px;
  color: #bdc3c7;
  font-weight: bold;
}

.sciana-podglad canvas {
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  width: 90px;
  height: 90px;
  transition: all 0.3s ease;
}

.sciana-podglad canvas:hover {
  border-color: #3498db;
  transform: scale(1.1);
}

.sciana-podglad.active {
  background: rgba(255, 255, 0, 0.2);
  border: 2px solid #ffff00;
}

.sciana-podglad.active canvas {
  border-color: #ffff00;
  box-shadow: 0 0 15px rgba(255, 255, 0, 0.5);
}

.score {
  font-size: 18px;
  font-weight: bold;
  margin: 8px 0;
  color: #3498db;
}

.active-face {
  font-size: 16px;
  margin: 10px 0;
  padding: 8px;
  background: rgba(52, 152, 219, 0.2);
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.controls {
  margin-top: 20px;
  font-size: 14px;
}

.control-group {
  margin: 15px 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.control-group strong {
  color: #f39c12;
  display: block;
  margin-bottom: 5px;
}

.control-group div {
  margin: 3px 0;
  color: #bdc3c7;
}

#wynik {
  color: #27ae60;
}

#pustePola {
  color: #e74c3c;
}

#aktywnaSciana {
  color: #f39c12;
  font-weight: bold;
  text-transform: uppercase;
}

.kostka-controls {
  margin: 15px 0;
  padding: 15px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(52, 152, 219, 0.3);
}

.rotation-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.horizontal-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-btn {
  background: rgba(52, 152, 219, 0.8);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 20px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(52, 152, 219, 1);
  transform: scale(1.1);
}

.control-btn:active {
  transform: scale(0.95);
}

.control-label {
  font-size: 12px;
  color: #bdc3c7;
  text-align: center;
  min-width: 60px;
}
