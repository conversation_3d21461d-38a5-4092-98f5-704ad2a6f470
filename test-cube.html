<!DOCTYPE html>
<html lang="pl">
<head>
  <title>Test kostki</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #info { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
            background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="info">
    <h3>Test kostki</h3>
    <div>Sprawdzamy czy Three.js działa...</div>
  </div>

  <script>
    console.log('Inicjalizacja testu...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    
    // Prosta kostka testowa
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    const cube = new THREE.Mesh(geometry, material);
    scene.add(cube);
    
    // Pozycja kamery
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);
    
    console.log('Kostka dodana do sceny');
    
    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      
      // Obracaj kostką
      cube.rotation.x += 0.01;
      cube.rotation.y += 0.01;
      
      renderer.render(scene, camera);
    }
    animate();
    
    console.log('Animacja uruchomiona');
  </script>
</body>
</html>
