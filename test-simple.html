<!DOCTYPE html>
<html>
<head>
    <title>Test Three.js</title>
    <style>
        body { margin: 0; background: #000; }
        canvas { display: block; }
    </style>
</head>
<body>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script>
        console.log('Rozpoczynam test Three.js...');
        
        if (typeof THREE === 'undefined') {
            console.error('THREE.js nie załadowane!');
            alert('THREE.js nie załadowane!');
        } else {
            console.log('THREE.js załadowane, wersja:', THREE.REVISION);
        }
        
        // Scena
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);
        
        // Kostka
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);
        
        // Pozycja kamery
        camera.position.z = 5;
        
        console.log('Scena utworzona, rozpoczynam renderowanie...');
        
        // Animacja
        function animate() {
            requestAnimationFrame(animate);
            
            cube.rotation.x += 0.01;
            cube.rotation.y += 0.01;
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        console.log('Test zakończony');
    </script>
</body>
</html>
