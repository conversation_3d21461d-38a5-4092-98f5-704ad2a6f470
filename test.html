<!DOCTYPE html>
<html lang="pl">
<head>
  <title>Test 2048 3D</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; }
    #info { position: absolute; top: 10px; left: 10px; color: white; font-family: Arial; }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="info">Test kostki 3D - jeśli widzisz czerwoną kostkę, Three.js działa</div>
  <script>
    console.log('Rozpoczynam test...');
    
    try {
      // Podstawowa scena
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer();
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x1a1a2e);
      document.body.appendChild(renderer.domElement);
      
      console.log('Renderer utworzony');
      
      // Prosta kostka testowa
      const geometry = new THREE.BoxGeometry(2, 2, 2);
      const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
      const cube = new THREE.Mesh(geometry, material);
      scene.add(cube);
      
      console.log('Kostka dodana do sceny');
      
      // Pozycja kamery
      camera.position.z = 5;
      
      // Animacja
      function animate() {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        renderer.render(scene, camera);
      }
      
      console.log('Rozpoczynam animację');
      animate();
      
    } catch (error) {
      console.error('Błąd w teście:', error);
      document.getElementById('info').innerHTML = 'BŁĄD: ' + error.message;
    }
  </script>
</body>
</html>
