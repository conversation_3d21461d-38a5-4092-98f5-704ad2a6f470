<!DOCTYPE html>
<html lang="pl">
<head>
  <title>2048 3D - Działająca wersja</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #ui { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
          background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    #preview { position: absolute; top: 10px; right: 10px; color: white; z-index: 100;
               background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    .face-preview { display: inline-block; margin: 5px; text-align: center; }
    .face-grid { width: 60px; height: 60px; border: 1px solid #fff; margin: 2px auto; }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="ui">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Wynik: <span id="score">0</span></div>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px; font-size: 12px;">
      <div>🎮 Strzałki - ruch cyfr</div>
      <div>🖱️ Mysz - obrót kostki</div>
      <div>Q/E - obrót warstw</div>
      <div>1-6 - obrót wierszy/kolumn</div>
    </div>
  </div>
  
  <div id="preview">
    <h4>Podgląd ścian</h4>
    <div id="faces"></div>
  </div>

  <script>
    console.log('Inicjalizacja...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Kolory ścian
    const faceColors = {
      front: '#dc3545',  back: '#fd7e14',   left: '#0d6efd',
      right: '#198754',  top: '#ffc107',    bottom: '#6f42c1'
    };
    
    // Dane gry - każda ściana ma tablicę 3x3
    const gameData = {};
    const tileColors = {}; // Kolory kafelków (mogą się różnić od kolorów ścian po obrotach)
    
    Object.keys(faceColors).forEach(face => {
      gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
      tileColors[face] = [];
      for(let x = 0; x < 3; x++) {
        tileColors[face][x] = [];
        for(let y = 0; y < 3; y++) {
          tileColors[face][x][y] = faceColors[face];
        }
      }
    });
    
    // Dodaj początkowe cyfry
    gameData.front[0][0] = 2;
    gameData.front[2][2] = 4;
    
    let activeFace = 'front';
    let score = 0;
    const tiles = {};
    
    // Tworzenie tekstury dla kafelka
    function createTileTexture(value, bgColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 128;
      canvas.height = 128;
      const ctx = canvas.getContext('2d');
      
      // Tło
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, 128, 128);
      
      // Ramka
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 2;
      ctx.strokeRect(2, 2, 124, 124);
      
      // Cyfra
      if (value > 0) {
        ctx.font = 'bold 40px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#fff';
        ctx.fillText(value.toString(), 64, 64);
      }
      
      return new THREE.CanvasTexture(canvas);
    }
    
    // Tworzenie kafelków
    function createTiles() {
      Object.keys(faceColors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const color = tileColors[face][x][y];
            const texture = createTileTexture(value, color);
            
            const geometry = new THREE.PlaneGeometry(1.5, 1.5);
            const material = new THREE.MeshBasicMaterial({ 
              map: texture,
              transparent: true,
              opacity: value > 0 ? 1.0 : 0.7
            });
            
            const tile = new THREE.Mesh(geometry, material);
            
            // Pozycjonowanie
            const posX = (x - 1) * 1.8;
            const posY = (y - 1) * 1.8;
            const offset = 3.5;
            
            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, -posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, posY);
                tile.rotation.x = Math.PI/2;
                break;
            }
            
            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });
    }
    
    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const color = tileColors[face][x][y];
            const texture = createTileTexture(value, color);
            tiles[face][x][y].material.map = texture;
            tiles[face][x][y].material.opacity = value > 0 ? 1.0 : 0.7;
            tiles[face][x][y].material.needsUpdate = true;
          }
        }
      });
      
      // Aktualizuj UI
      document.getElementById('score').textContent = score;
      document.getElementById('active').textContent = activeFace.toUpperCase();
    }
    
    // Pozycja kamery
    camera.position.set(12, 8, 12);
    camera.lookAt(0, 0, 0);
    
    // Sterowanie myszą
    let isDragging = false;
    let previousMouse = { x: 0, y: 0 };
    
    document.addEventListener('mousedown', (e) => {
      isDragging = true;
      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
    });
    
    document.addEventListener('mouseup', () => {
      isDragging = false;
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - previousMouse.x;
      const deltaY = e.clientY - previousMouse.y;
      
      cubeGroup.rotation.y += deltaX * 0.01;
      cubeGroup.rotation.x -= deltaY * 0.01;
      
      // Ograniczenia
      cubeGroup.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, cubeGroup.rotation.x));
      
      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
    });
    
    // Inicjalizacja
    createTiles();
    updateTiles();
    
    console.log('Kostka utworzona!');
    
    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    animate();
  </script>
</body>
</html>
