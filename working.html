<!DOCTYPE html>
<html lang="pl">
<head>
  <title>2048 3D - Działająca wersja</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #ui { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
          background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    #preview { position: absolute; top: 10px; right: 10px; color: white; z-index: 100;
               background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    .face-preview { display: inline-block; margin: 5px; text-align: center; }
    .face-grid { width: 60px; height: 60px; border: 1px solid #fff; margin: 2px auto; }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="ui">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Wynik: <span id="score">0</span></div>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px; font-size: 12px;">
      <div>🖱️ Mysz - obrót kostki</div>
      <div>🔄 Q/E - obrót górnej warstwy</div>
      <div>🔄 Z/X - obrót dolnej warstwy</div>
      <div>✨ Kolory ścian podążają za obrotami!</div>
    </div>
  </div>
  
  <div id="preview">
    <h4>Podgląd ścian</h4>
    <div id="faces"></div>
  </div>

  <script>
    console.log('Inicjalizacja...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Kolory ścian
    const faceColors = {
      front: '#dc3545',  back: '#fd7e14',   left: '#0d6efd',
      right: '#198754',  top: '#ffc107',    bottom: '#6f42c1'
    };
    
    // Dane gry - każda ściana ma tablicę 3x3
    const gameData = {};
    const tileColors = {}; // Kolory kafelków (mogą się różnić od kolorów ścian po obrotach)
    
    Object.keys(faceColors).forEach(face => {
      gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
      tileColors[face] = [];
      for(let x = 0; x < 3; x++) {
        tileColors[face][x] = [];
        for(let y = 0; y < 3; y++) {
          tileColors[face][x][y] = faceColors[face];
        }
      }
    });
    
    // Dodaj początkowe cyfry na różnych ścianach
    gameData.front[0][0] = 2;
    gameData.front[2][2] = 4;
    gameData.top[1][1] = 8;
    gameData.right[0][1] = 2;
    gameData.left[2][0] = 16;
    
    let activeFace = 'front';
    let score = 0;
    const tiles = {};
    
    // Kolory dla cyfr (jak w oryginalnym 2048)
    const numberColors = {
      2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
      32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
      512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
    };

    function getTextColor(value) {
      return [2, 4].includes(value) ? '#000000' : '#ffffff';
    }

    // Tworzenie tekstury dla kafelka
    function createTileTexture(value, faceColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 256;
      const ctx = canvas.getContext('2d');

      // TŁO ŚCIANKI - zawsze kolor ściany (nie zmienia się z cyfrą!)
      ctx.fillStyle = faceColor;
      ctx.fillRect(0, 0, 256, 256);

      // Ramka ścianki
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 4;
      ctx.strokeRect(4, 4, 248, 248);

      // Jeśli jest cyfra, narysuj kafelek z cyfrą
      if (value > 0) {
        const numberColor = numberColors[value] || '#3c3a32';
        const textColor = getTextColor(value);

        // Kafelek z cyfrą (mniejszy niż cała ścianka)
        ctx.fillStyle = numberColor;
        ctx.fillRect(20, 20, 216, 216);

        // Ramka kafelka
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeRect(20, 20, 216, 216);

        // Cyfra
        ctx.font = 'bold 80px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Cień cyfry
        ctx.fillStyle = textColor === '#ffffff' ? 'rgba(0,0,0,0.3)' : 'rgba(255,255,255,0.3)';
        ctx.fillText(value.toString(), 130, 130);

        // Główna cyfra
        ctx.fillStyle = textColor;
        ctx.fillText(value.toString(), 128, 128);
      }

      return new THREE.CanvasTexture(canvas);
    }
    
    // Tworzenie kafelków
    function createTiles() {
      Object.keys(faceColors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y]; // Kolor ściany dla tego kafelka
            const texture = createTileTexture(value, faceColor);
            
            const geometry = new THREE.PlaneGeometry(1.5, 1.5);
            const material = new THREE.MeshBasicMaterial({ 
              map: texture,
              transparent: true,
              opacity: value > 0 ? 1.0 : 0.7
            });
            
            const tile = new THREE.Mesh(geometry, material);
            
            // Pozycjonowanie
            const posX = (x - 1) * 1.8;
            const posY = (y - 1) * 1.8;
            const offset = 3.5;
            
            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, -posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, posY);
                tile.rotation.x = Math.PI/2;
                break;
            }
            
            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });
    }
    
    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y]; // Kolor ściany dla tego kafelka
            const texture = createTileTexture(value, faceColor);
            tiles[face][x][y].material.map = texture;
            tiles[face][x][y].material.opacity = 1.0; // Zawsze pełna przezroczystość
            tiles[face][x][y].material.needsUpdate = true;
          }
        }
      });
      
      // Aktualizuj UI
      document.getElementById('score').textContent = score;
      document.getElementById('active').textContent = activeFace.toUpperCase();
    }
    
    // Pozycja kamery
    camera.position.set(12, 8, 12);
    camera.lookAt(0, 0, 0);
    
    // Sterowanie myszą
    let isDragging = false;
    let previousMouse = { x: 0, y: 0 };
    
    document.addEventListener('mousedown', (e) => {
      isDragging = true;
      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
    });
    
    document.addEventListener('mouseup', () => {
      isDragging = false;
    });
    
    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - previousMouse.x;
      const deltaY = e.clientY - previousMouse.y;
      
      cubeGroup.rotation.y += deltaX * 0.01;
      cubeGroup.rotation.x -= deltaY * 0.01;
      
      // Ograniczenia
      cubeGroup.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, cubeGroup.rotation.x));
      
      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
    });

    // Funkcje obracania warstw kostki Rubika
    function rotateLayer(layer, direction) {
      console.log(`Obracanie warstwy ${layer} w kierunku ${direction}`);

      // Zbierz dane z warstwy
      let layerData = {};
      let layerColors = {};

      switch(layer) {
        case 'top': // Górna warstwa (y = 2)
          layerData = {
            front: [gameData.front[0][2], gameData.front[1][2], gameData.front[2][2]],
            right: [gameData.right[0][2], gameData.right[1][2], gameData.right[2][2]],
            back: [gameData.back[0][2], gameData.back[1][2], gameData.back[2][2]],
            left: [gameData.left[0][2], gameData.left[1][2], gameData.left[2][2]]
          };
          layerColors = {
            front: [tileColors.front[0][2], tileColors.front[1][2], tileColors.front[2][2]],
            right: [tileColors.right[0][2], tileColors.right[1][2], tileColors.right[2][2]],
            back: [tileColors.back[0][2], tileColors.back[1][2], tileColors.back[2][2]],
            left: [tileColors.left[0][2], tileColors.left[1][2], tileColors.left[2][2]]
          };
          break;
        case 'bottom': // Dolna warstwa (y = 0)
          layerData = {
            front: [gameData.front[0][0], gameData.front[1][0], gameData.front[2][0]],
            left: [gameData.left[0][0], gameData.left[1][0], gameData.left[2][0]],
            back: [gameData.back[0][0], gameData.back[1][0], gameData.back[2][0]],
            right: [gameData.right[0][0], gameData.right[1][0], gameData.right[2][0]]
          };
          layerColors = {
            front: [tileColors.front[0][0], tileColors.front[1][0], tileColors.front[2][0]],
            left: [tileColors.left[0][0], tileColors.left[1][0], tileColors.left[2][0]],
            back: [tileColors.back[0][0], tileColors.back[1][0], tileColors.back[2][0]],
            right: [tileColors.right[0][0], tileColors.right[1][0], tileColors.right[2][0]]
          };
          break;
      }

      // Obróć dane
      const faces = Object.keys(layerData);
      const rotatedData = {};
      const rotatedColors = {};

      for (let i = 0; i < faces.length; i++) {
        const currentFace = faces[i];
        const nextFace = faces[(i + direction + faces.length) % faces.length];
        rotatedData[currentFace] = layerData[nextFace];
        rotatedColors[currentFace] = layerColors[nextFace];
      }

      // Zastosuj obrócone dane
      switch(layer) {
        case 'top':
          for (let x = 0; x < 3; x++) {
            gameData.front[x][2] = rotatedData.front[x];
            gameData.right[x][2] = rotatedData.right[x];
            gameData.back[x][2] = rotatedData.back[x];
            gameData.left[x][2] = rotatedData.left[x];
            tileColors.front[x][2] = rotatedColors.front[x];
            tileColors.right[x][2] = rotatedColors.right[x];
            tileColors.back[x][2] = rotatedColors.back[x];
            tileColors.left[x][2] = rotatedColors.left[x];
          }
          // Obróć też samą ścianę top
          rotateFace('top', direction);
          break;
        case 'bottom':
          for (let x = 0; x < 3; x++) {
            gameData.front[x][0] = rotatedData.front[x];
            gameData.left[x][0] = rotatedData.left[x];
            gameData.back[x][0] = rotatedData.back[x];
            gameData.right[x][0] = rotatedData.right[x];
            tileColors.front[x][0] = rotatedColors.front[x];
            tileColors.left[x][0] = rotatedColors.left[x];
            tileColors.back[x][0] = rotatedColors.back[x];
            tileColors.right[x][0] = rotatedColors.right[x];
          }
          // Obróć też samą ścianę bottom
          rotateFace('bottom', direction);
          break;
      }

      updateTiles();
    }

    // Obrót samej ściany (macierz 3x3)
    function rotateFace(face, direction) {
      const oldData = [];
      const oldColors = [];

      // Zbierz dane
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          oldData[x * 3 + y] = gameData[face][x][y];
          oldColors[x * 3 + y] = tileColors[face][x][y];
        }
      }

      // Obróć macierz 3x3
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          if (direction === 1) { // W prawo
            gameData[face][x][y] = oldData[(2 - y) * 3 + x];
            tileColors[face][x][y] = oldColors[(2 - y) * 3 + x];
          } else { // W lewo
            gameData[face][x][y] = oldData[y * 3 + (2 - x)];
            tileColors[face][x][y] = oldColors[y * 3 + (2 - x)];
          }
        }
      }
    }

    // Sterowanie klawiaturą
    document.addEventListener('keydown', (e) => {
      switch(e.code) {
        case 'KeyQ':
          rotateLayer('top', 1);
          break;
        case 'KeyE':
          rotateLayer('top', -1);
          break;
        case 'KeyZ':
          rotateLayer('bottom', 1);
          break;
        case 'KeyX':
          rotateLayer('bottom', -1);
          break;
      }
    });

    // Inicjalizacja
    createTiles();
    updateTiles();
    
    console.log('Kostka utworzona!');
    
    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    animate();
  </script>
</body>
</html>
