<!DOCTYPE html>
<html lang="pl">
<head>
  <title>Prosta kostka 2048</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #info { position: absolute; top: 10px; left: 10px; color: white; z-index: 100;
            background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; max-width: 300px; }

    .cube-controls {
      margin-top: 20px;
      padding: 15px;
      background: rgba(0,0,0,0.8);
      border-radius: 8px;
    }

    .rotation-controls {
      margin: 10px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .rotation-controls label {
      min-width: 120px;
      color: white;
      font-size: 12px;
    }

    .rotation-controls input[type="range"] {
      flex: 1;
      height: 6px;
      background: #333;
      border-radius: 3px;
      outline: none;
    }

    .rotation-controls input[type="range"]::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: #4CAF50;
      border-radius: 50%;
      cursor: pointer;
    }

    .rotation-controls span {
      min-width: 40px;
      color: #4CAF50;
      font-size: 12px;
      text-align: right;
    }

    .cube-controls button {
      margin-top: 10px;
      padding: 8px 16px;
      background: #FF5722;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 12px;
    }

    .cube-controls button:hover {
      background: #E64A19;
    }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="info">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px; font-size: 11px;">
      <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
      <div><strong>🖱️ Mysz</strong> - obrót kostki (przeciągnij)</div>
      <div><strong>🔄 Warstwy:</strong> Q/E (góra), W/S (środek), A/D (boki)</div>
    </div>

    <!-- Suwaki do obracania kostki -->
    <div class="cube-controls">
      <h4 style="color: white; margin: 0 0 10px 0;">🎛️ Sterowanie widokiem</h4>
      <div class="rotation-controls">
        <label>Obrót X (góra-dół):</label>
        <input type="range" id="rotationX" min="-180" max="180" value="-17" step="1">
        <span id="rotationXValue">-17°</span>
      </div>
      <div class="rotation-controls">
        <label>Obrót Y (lewo-prawo):</label>
        <input type="range" id="rotationY" min="-180" max="180" value="17" step="1">
        <span id="rotationYValue">17°</span>
      </div>
      <button onclick="resetCubeRotation()">🔄 Reset widoku</button>
    </div>
  </div>
  <script>
    console.log('Rozpoczynam prostą kostkę...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const light = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(light);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);
    
    // Kolory ścian
    const colors = {
      front: 0xff0000,  // czerwony
      back: 0x00ff00,   // zielony  
      left: 0x0000ff,   // niebieski
      right: 0xffff00,  // żółty
      top: 0xff00ff,    // magenta
      bottom: 0x00ffff  // cyan
    };
    
    // Tworzenie ścian kostki
    const faceSize = 3;
    const spacing = 1.1;
    const offset = 3;
    
    // Dane gry - prosta tablica 3x3 dla każdej ściany
    const gameData = {
      front: [[0,2,0],[0,0,0],[0,0,4]],
      back: [[0,0,0],[0,0,0],[0,0,0]],
      left: [[0,0,0],[0,0,0],[0,0,0]],
      right: [[0,0,0],[0,0,0],[0,0,0]],
      top: [[0,0,0],[0,0,0],[0,0,0]],
      bottom: [[0,0,0],[0,0,0],[0,0,0]]
    };
    
    let activeFace = 'front';
    const tiles = {};
    
    // Tworzenie kafelków
    function createTiles() {
      Object.keys(colors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const geometry = new THREE.PlaneGeometry(0.9, 0.9);
            const material = new THREE.MeshBasicMaterial({ 
              color: colors[face],
              transparent: true,
              opacity: gameData[face][x][y] > 0 ? 1.0 : 0.3
            });
            
            const tile = new THREE.Mesh(geometry, material);
            
            // Pozycjonowanie
            const posX = (x - 1) * spacing;
            const posY = (y - 1) * spacing;
            
            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, -posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, posY);
                tile.rotation.x = Math.PI/2;
                break;
            }
            
            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });
    }
    
    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            tiles[face][x][y].material.opacity = value > 0 ? 1.0 : 0.3;
            // Można dodać teksty z cyframi później
          }
        }
      });
    }
    
    // Sterowanie
    document.addEventListener('keydown', (event) => {
      console.log('Klawisz:', event.code);
      
      switch(event.code) {
        case 'ArrowLeft':
          // Przesuń w lewo na aktywnej ścianie
          console.log('Ruch w lewo');
          break;
        case 'ArrowRight':
          console.log('Ruch w prawo');
          break;
        case 'KeyQ':
          // Obróć górną warstwę
          console.log('Obrót górnej warstwy');
          break;
      }
    });
    
    // Pozycja kamery
    camera.position.set(8, 6, 8);
    camera.lookAt(0, 0, 0);

    // Ustaw początkową pozycję kostki
    cubeGroup.rotation.x = -0.3;
    cubeGroup.rotation.y = 0.3;
    
    // Funkcje suwaków
    function updateSliders() {
      const rotXDeg = (cubeGroup.rotation.x * 180 / Math.PI).toFixed(0);
      const rotYDeg = (cubeGroup.rotation.y * 180 / Math.PI).toFixed(0);

      document.getElementById('rotationX').value = rotXDeg;
      document.getElementById('rotationY').value = rotYDeg;
      document.getElementById('rotationXValue').textContent = rotXDeg + '°';
      document.getElementById('rotationYValue').textContent = rotYDeg + '°';
    }

    function resetCubeRotation() {
      cubeGroup.rotation.x = -0.3;
      cubeGroup.rotation.y = 0.3;
      updateSliders();
    }

    // Udostępnij funkcję globalnie
    window.resetCubeRotation = resetCubeRotation;

    // Obsługa suwaków
    document.getElementById('rotationX').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.x = degrees * Math.PI / 180;
      document.getElementById('rotationXValue').textContent = degrees + '°';
    });

    document.getElementById('rotationY').addEventListener('input', (e) => {
      const degrees = parseFloat(e.target.value);
      cubeGroup.rotation.y = degrees * Math.PI / 180;
      document.getElementById('rotationYValue').textContent = degrees + '°';
    });

    // Obsługa myszy
    let isDragging = false;
    let lastMouseX = 0;
    let lastMouseY = 0;

    renderer.domElement.addEventListener('mousedown', (event) => {
      isDragging = true;
      lastMouseX = event.clientX;
      lastMouseY = event.clientY;
    });

    renderer.domElement.addEventListener('mousemove', (event) => {
      if (!isDragging) return;

      const deltaX = event.clientX - lastMouseX;
      const deltaY = event.clientY - lastMouseY;

      cubeGroup.rotation.y += deltaX * 0.01;
      cubeGroup.rotation.x += deltaY * 0.01;

      lastMouseX = event.clientX;
      lastMouseY = event.clientY;

      updateSliders();
    });

    renderer.domElement.addEventListener('mouseup', () => {
      isDragging = false;
    });

    renderer.domElement.addEventListener('mouseleave', () => {
      isDragging = false;
    });

    // Inicjalizacja
    createTiles();
    updateTiles();
    updateSliders();

    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }

    console.log('Kostka utworzona, rozpoczynam animację');
    animate();
  </script>
</body>
</html>
