<!DOCTYPE html>
<html lang="pl">
<head>
  <title>2048 3D - Działająca wersja</title>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; overflow: hidden; background: #1a1a2e; font-family: Arial; }
    #ui { position: absolute; top: 10px; left: 10px; color: white; z-index: 100; 
          background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; }
    #preview { position: absolute; top: 10px; right: 10px; color: white; z-index: 100;
               background: rgba(0,0,0,0.9); padding: 20px; border-radius: 15px;
               backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.1); }
    .face-row { display: flex; gap: 10px; margin-bottom: 10px; }
    .face-preview { text-align: center; cursor: pointer; padding: 8px; border-radius: 8px;
                    transition: all 0.3s ease; background: rgba(255,255,255,0.05); }
    .face-preview:hover { background: rgba(255,255,255,0.15); transform: scale(1.05); }
    .face-preview.active { background: rgba(255,255,0,0.3); border: 2px solid #ffff00; }
    .face-name { font-size: 10px; margin-bottom: 5px; font-weight: bold; }
    .face-preview canvas { border-radius: 6px; border: 1px solid rgba(255,255,255,0.2); }
    #mobile-controls { position: absolute; bottom: 10px; left: 10px; color: white; z-index: 100;
                       background: rgba(0,0,0,0.9); padding: 15px; border-radius: 10px; }
    .control-section { margin-bottom: 10px; }
    .control-section h4 { margin: 0 0 10px 0; font-size: 14px; }
    .control-buttons { display: flex; gap: 10px; }
    .control-buttons button { padding: 8px 12px; border: none; border-radius: 5px;
                              background: #4CAF50; color: white; cursor: pointer; font-size: 12px; }
    .control-buttons button:hover { background: #45a049; }
    .control-buttons button:active { transform: scale(0.95); }
  </style>
  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
</head>
<body>
  <div id="ui">
    <h3>2048 3D Kostka Rubika</h3>
    <div>Wynik: <span id="score">0</span></div>
    <div>Aktywna ściana: <span id="active">FRONT</span></div>
    <div style="margin-top: 10px; font-size: 11px;">
      <div><strong>🎮 Strzałki</strong> - gra 2048 na aktywnej ścianie</div>
      <div><strong>🖱️ Mysz</strong> - obrót kostki (poziomo/pionowo)</div>
      <div><strong>🔄 Warstwy:</strong> Q/E (góra), W/S (środek), Z/X (dół)</div>
      <div><strong>🔄 Kolumny:</strong> A (lewa), C (środek), D (prawa)</div>
      <div><strong>📋 Ściany:</strong> 1-6 (szybka zmiana aktywnej)</div>
      <div><strong>👆 Kliknij</strong> podgląd aby zmienić aktywną ścianę</div>
    </div>
  </div>
  
  <div id="preview">
    <h4>Podgląd ścian</h4>
    <div id="faces">
      <div class="face-row">
        <div class="face-preview" data-face="front">
          <div class="face-name">FRONT</div>
          <canvas id="canvas-front" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="back">
          <div class="face-name">BACK</div>
          <canvas id="canvas-back" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="left">
          <div class="face-name">LEFT</div>
          <canvas id="canvas-left" width="120" height="120"></canvas>
        </div>
      </div>
      <div class="face-row">
        <div class="face-preview" data-face="right">
          <div class="face-name">RIGHT</div>
          <canvas id="canvas-right" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="top">
          <div class="face-name">TOP</div>
          <canvas id="canvas-top" width="120" height="120"></canvas>
        </div>
        <div class="face-preview" data-face="bottom">
          <div class="face-name">BOTTOM</div>
          <canvas id="canvas-bottom" width="120" height="120"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Panel sterowania mobilnego -->
  <div id="mobile-controls" style="display: none;">
    <div class="control-section">
      <h4>Obrót warstwy</h4>
      <div class="control-buttons">
        <button onclick="rotateSelectedLayer(-1)">⟲ Lewo</button>
        <button onclick="rotateSelectedLayer(1)">⟳ Prawo</button>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/three@0.150.1/build/three.min.js"></script>
  <script>
    console.log('Inicjalizacja...');
    
    // Scena
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x1a1a2e);
    document.body.appendChild(renderer.domElement);
    
    // Oświetlenie
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);
    
    // Grupa kostki
    const cubeGroup = new THREE.Group();
    scene.add(cubeGroup);

    // Dodaj podstawową strukturę kostki (linie podziału)
    function createCubeStructure() {
      const edgesGeometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(7, 7, 7));
      const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x333333, linewidth: 2 });
      const cubeEdges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
      cubeGroup.add(cubeEdges);

      // Linie podziału na każdej ścianie
      const lineMaterial = new THREE.LineBasicMaterial({ color: 0x666666, linewidth: 1 });

      // Linie poziome i pionowe na każdej ścianie
      for (let i = -1; i <= 1; i += 2) {
        // Front/Back
        for (let j = -1.8; j <= 1.8; j += 1.8) {
          const hLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(-3.5, j, 3.5 * i),
            new THREE.Vector3(3.5, j, 3.5 * i)
          ]);
          const vLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(j, -3.5, 3.5 * i),
            new THREE.Vector3(j, 3.5, 3.5 * i)
          ]);
          cubeGroup.add(new THREE.Line(hLineGeometry, lineMaterial));
          cubeGroup.add(new THREE.Line(vLineGeometry, lineMaterial));
        }

        // Left/Right
        for (let j = -1.8; j <= 1.8; j += 1.8) {
          const hLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(3.5 * i, j, -3.5),
            new THREE.Vector3(3.5 * i, j, 3.5)
          ]);
          const vLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(3.5 * i, -3.5, j),
            new THREE.Vector3(3.5 * i, 3.5, j)
          ]);
          cubeGroup.add(new THREE.Line(hLineGeometry, lineMaterial));
          cubeGroup.add(new THREE.Line(vLineGeometry, lineMaterial));
        }

        // Top/Bottom
        for (let j = -1.8; j <= 1.8; j += 1.8) {
          const hLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(-3.5, 3.5 * i, j),
            new THREE.Vector3(3.5, 3.5 * i, j)
          ]);
          const vLineGeometry = new THREE.BufferGeometry().setFromPoints([
            new THREE.Vector3(j, 3.5 * i, -3.5),
            new THREE.Vector3(j, 3.5 * i, 3.5)
          ]);
          cubeGroup.add(new THREE.Line(hLineGeometry, lineMaterial));
          cubeGroup.add(new THREE.Line(vLineGeometry, lineMaterial));
        }
      }
    }
    
    // Ładne pastelowe kolory ścian
    const faceColors = {
      front: '#ff9999',   // Pastelowy czerwony
      back: '#ffcc99',    // Pastelowy pomarańczowy
      left: '#99ccff',    // Pastelowy niebieski
      right: '#99ff99',   // Pastelowy zielony
      top: '#ffff99',     // Pastelowy żółty
      bottom: '#cc99ff'   // Pastelowy fioletowy
    };
    
    // Dane gry - każda ściana ma tablicę 3x3
    const gameData = {};
    const tileColors = {}; // Kolory kafelków (mogą się różnić od kolorów ścian po obrotach)
    
    Object.keys(faceColors).forEach(face => {
      gameData[face] = [[0,0,0],[0,0,0],[0,0,0]];
      tileColors[face] = [];
      for(let x = 0; x < 3; x++) {
        tileColors[face][x] = [];
        for(let y = 0; y < 3; y++) {
          tileColors[face][x][y] = faceColors[face];
        }
      }
    });
    
    // Dodaj początkowe cyfry na różnych ścianach
    gameData.front[0][0] = 2;
    gameData.front[2][2] = 4;
    gameData.top[1][1] = 8;
    gameData.right[0][1] = 2;
    gameData.left[2][0] = 16;
    
    let activeFace = 'front';
    let score = 0;
    const tiles = {};
    let isAnimating = false;
    
    // Kolory dla cyfr (jak w oryginalnym 2048)
    const numberColors = {
      2: '#776e65', 4: '#ede0c8', 8: '#f2b179', 16: '#f59563',
      32: '#f67c5f', 64: '#f65e3b', 128: '#edcf72', 256: '#edcc61',
      512: '#edc850', 1024: '#edc53f', 2048: '#edc22e'
    };

    function getTextColor(value) {
      return [2, 4].includes(value) ? '#000000' : '#ffffff';
    }

    // Tworzenie ładnej tekstury dla kafelka
    function createTileTexture(value, faceColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 512;
      canvas.height = 512;
      const ctx = canvas.getContext('2d');

      // TŁO ŚCIANKI - zawsze kolor ściany (nie zmienia się z cyfrą!)
      const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
      gradient.addColorStop(0, faceColor);
      gradient.addColorStop(1, darkenColor(faceColor, 0.15));
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 512, 512);

      // Delikatna ramka ścianki
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.lineWidth = 2;
      ctx.strokeRect(2, 2, 508, 508);

      // Jeśli jest cyfra, narysuj zaokrąglony kafelek z cyfrą
      if (value > 0) {
        const numberColor = numberColors[value] || '#f0f0f0';
        const textColor = getTextColor(value);

        // Zaokrąglony kafelek z cyfrą
        const margin = 40;
        const radius = 30;

        ctx.fillStyle = numberColor;
        ctx.beginPath();
        ctx.roundRect(margin, margin, 512 - 2*margin, 512 - 2*margin, radius);
        ctx.fill();

        // Delikatny cień kafelka
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 3;
        ctx.shadowOffsetY = 3;

        // Gradient na kafelku
        const tileGradient = ctx.createRadialGradient(256, 200, 0, 256, 256, 200);
        tileGradient.addColorStop(0, lightenColor(numberColor, 0.1));
        tileGradient.addColorStop(1, numberColor);
        ctx.fillStyle = tileGradient;
        ctx.fill();

        // Reset cienia
        ctx.shadowColor = 'transparent';

        // Cyfra
        ctx.font = 'bold 120px "Segoe UI", Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Cień cyfry
        ctx.fillStyle = textColor === '#ffffff' ? 'rgba(0,0,0,0.4)' : 'rgba(255,255,255,0.4)';
        ctx.fillText(value.toString(), 258, 258);

        // Główna cyfra
        ctx.fillStyle = textColor;
        ctx.fillText(value.toString(), 256, 256);
      }

      return new THREE.CanvasTexture(canvas);
    }

    // Funkcje pomocnicze do kolorów
    function darkenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
      const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
      const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }

    function lightenColor(color, amount) {
      const hex = color.replace('#', '');
      const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
      const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
      const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
      return `rgb(${r}, ${g}, ${b})`;
    }
    
    // Tworzenie kafelków
    function createTiles() {
      Object.keys(faceColors).forEach(face => {
        tiles[face] = [];
        for (let x = 0; x < 3; x++) {
          tiles[face][x] = [];
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y]; // Kolor ściany dla tego kafelka
            const texture = createTileTexture(value, faceColor);
            
            const geometry = new THREE.PlaneGeometry(1.5, 1.5);
            const material = new THREE.MeshBasicMaterial({ 
              map: texture,
              transparent: true,
              opacity: value > 0 ? 1.0 : 0.7
            });
            
            const tile = new THREE.Mesh(geometry, material);
            
            // Pozycjonowanie
            const posX = (x - 1) * 1.8;
            const posY = (y - 1) * 1.8;
            const offset = 3.5;
            
            switch(face) {
              case 'front':
                tile.position.set(posX, posY, offset);
                break;
              case 'back':
                tile.position.set(-posX, posY, -offset);
                tile.rotation.y = Math.PI;
                break;
              case 'right':
                tile.position.set(offset, posY, -posX);
                tile.rotation.y = Math.PI/2;
                break;
              case 'left':
                tile.position.set(-offset, posY, posX);
                tile.rotation.y = -Math.PI/2;
                break;
              case 'top':
                tile.position.set(posX, offset, -posY);
                tile.rotation.x = -Math.PI/2;
                break;
              case 'bottom':
                tile.position.set(posX, -offset, posY);
                tile.rotation.x = Math.PI/2;
                break;
            }
            
            tiles[face][x][y] = tile;
            cubeGroup.add(tile);
          }
        }
      });
    }
    
    // Aktualizacja kafelków
    function updateTiles() {
      Object.keys(gameData).forEach(face => {
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const faceColor = tileColors[face][x][y]; // Kolor ściany dla tego kafelka
            const texture = createTileTexture(value, faceColor);
            tiles[face][x][y].material.map = texture;
            tiles[face][x][y].material.opacity = 1.0; // Zawsze pełna przezroczystość
            tiles[face][x][y].material.needsUpdate = true;
          }
        }
      });

      // Aktualizuj podgląd ścian
      updateFacePreviews();

      // Aktualizuj UI
      document.getElementById('score').textContent = score;
      document.getElementById('active').textContent = activeFace.toUpperCase();
    }

    // Aktualizacja podglądu ścian
    function updateFacePreviews() {
      Object.keys(gameData).forEach(face => {
        const canvas = document.getElementById(`canvas-${face}`);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, 120, 120);

        // Tło ściany
        const baseFaceColor = faceColors[face];
        ctx.fillStyle = baseFaceColor;
        ctx.fillRect(0, 0, 120, 120);

        // Rysuj siatkę 3x3
        for (let x = 0; x < 3; x++) {
          for (let y = 0; y < 3; y++) {
            const value = gameData[face][x][y];
            const tileColor = tileColors[face][x][y];
            const cellSize = 40;
            const startX = x * cellSize;
            const startY = y * cellSize;

            // Tło kafelka (kolor ściany)
            ctx.fillStyle = tileColor;
            ctx.fillRect(startX, startY, cellSize, cellSize);

            // Jeśli jest cyfra, narysuj kafelek z cyfrą
            if (value > 0) {
              const numberColor = numberColors[value] || '#f0f0f0';
              const textColor = getTextColor(value);

              // Zaokrąglony kafelek
              ctx.fillStyle = numberColor;
              ctx.beginPath();
              ctx.roundRect(startX + 3, startY + 3, cellSize - 6, cellSize - 6, 4);
              ctx.fill();

              // Cyfra
              ctx.font = 'bold 16px Arial';
              ctx.textAlign = 'center';
              ctx.textBaseline = 'middle';
              ctx.fillStyle = textColor;
              ctx.fillText(value.toString(), startX + cellSize/2, startY + cellSize/2);
            }

            // Ramka kafelka
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.lineWidth = 1;
            ctx.strokeRect(startX, startY, cellSize, cellSize);
          }
        }

        // Podświetl aktywną ścianę
        const preview = document.querySelector(`[data-face="${face}"]`);
        if (face === activeFace) {
          preview.classList.add('active');
        } else {
          preview.classList.remove('active');
        }
      });
    }
    
    // Pozycja kamery
    camera.position.set(12, 8, 12);
    camera.lookAt(0, 0, 0);
    
    // Lepsze sterowanie myszą
    let isDragging = false;
    let previousMouse = { x: 0, y: 0 };
    let dragStartMouse = { x: 0, y: 0 };
    let dragDirection = null; // 'horizontal', 'vertical', null

    document.addEventListener('mousedown', (e) => {
      if (isAnimating) return;
      isDragging = true;
      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
      dragStartMouse.x = e.clientX;
      dragStartMouse.y = e.clientY;
      dragDirection = null;
    });

    document.addEventListener('mouseup', () => {
      isDragging = false;
      dragDirection = null;
    });

    // Obsługa prawego przycisku myszy - wybór warstwy
    let selectedLayer = null;

    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();

      // Raycast do znalezienia klikniętego kafelka
      const mouse = new THREE.Vector2();
      mouse.x = (e.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(e.clientY / window.innerHeight) * 2 + 1;

      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(mouse, camera);

      const intersects = raycaster.intersectObjects(cubeGroup.children.filter(child => child.type === 'Mesh'));

      if (intersects.length > 0) {
        const intersectedObject = intersects[0].object;
        const userData = intersectedObject.userData;

        if (userData && userData.sciana) {
          // Określ warstwę na podstawie pozycji kafelka
          const face = userData.sciana;
          const position = intersectedObject.position;

          // Logika określania warstwy - uproszczona
          if (Math.abs(position.y - 1.8) < 0.1) selectedLayer = 'top';
          else if (Math.abs(position.y + 1.8) < 0.1) selectedLayer = 'bottom';
          else if (Math.abs(position.y) < 0.1) selectedLayer = 'middle';
          else if (Math.abs(position.x - 1.8) < 0.1) selectedLayer = 'right';
          else if (Math.abs(position.x + 1.8) < 0.1) selectedLayer = 'left';
          else selectedLayer = 'center';

          // Pokaż panel sterowania
          const mobileControls = document.getElementById('mobile-controls');
          mobileControls.style.display = 'block';
          mobileControls.style.left = e.clientX + 'px';
          mobileControls.style.top = e.clientY + 'px';

          console.log('Wybrana warstwa:', selectedLayer);
        }
      }
    });

    // Ukryj panel przy kliknięciu lewym przyciskiem
    document.addEventListener('click', (e) => {
      if (!e.target.closest('#mobile-controls')) {
        document.getElementById('mobile-controls').style.display = 'none';
      }
    });

    // Funkcja obrotu wybranej warstwy
    function rotateSelectedLayer(direction) {
      if (selectedLayer) {
        rotateLayer(selectedLayer, direction);
        document.getElementById('mobile-controls').style.display = 'none';
      }
    }

    // Udostępnij funkcję globalnie
    window.rotateSelectedLayer = rotateSelectedLayer;

    document.addEventListener('mousemove', (e) => {
      if (!isDragging || isAnimating) return;

      const deltaX = e.clientX - previousMouse.x;
      const deltaY = e.clientY - previousMouse.y;
      const totalDeltaX = Math.abs(e.clientX - dragStartMouse.x);
      const totalDeltaY = Math.abs(e.clientY - dragStartMouse.y);

      // Określ kierunek przeciągania po pierwszym ruchu
      if (!dragDirection && (totalDeltaX > 5 || totalDeltaY > 5)) {
        dragDirection = totalDeltaX > totalDeltaY ? 'horizontal' : 'vertical';
      }

      // Obracaj tylko w określonym kierunku
      if (dragDirection === 'horizontal') {
        cubeGroup.rotation.y += deltaX * 0.01;
      } else if (dragDirection === 'vertical') {
        cubeGroup.rotation.x -= deltaY * 0.01;
        // Ograniczenia dla obrotu pionowego
        cubeGroup.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, cubeGroup.rotation.x));
      }

      previousMouse.x = e.clientX;
      previousMouse.y = e.clientY;
    });

    // Animacja obrotu kostki
    function animateRotation(axis, angle, duration = 500) {
      if (isAnimating) return;
      isAnimating = true;

      const startRotation = {};
      startRotation[axis] = cubeGroup.rotation[axis];
      const targetRotation = startRotation[axis] + angle;

      const startTime = Date.now();

      function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);

        cubeGroup.rotation[axis] = startRotation[axis] + (targetRotation - startRotation[axis]) * easeOut;

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          isAnimating = false;
        }
      }

      animate();
    }

    // Funkcje obracania warstw kostki Rubika
    function rotateLayer(layer, direction) {
      if (isAnimating) return;
      console.log(`Obracanie warstwy ${layer} w kierunku ${direction}`);

      // Animacja obrotu kostki dla lepszego efektu wizualnego
      switch(layer) {
        case 'top':
        case 'middle':
        case 'bottom':
          animateRotation('y', direction * Math.PI / 6, 300);
          break;
        case 'left':
        case 'center':
        case 'right':
          animateRotation('x', direction * Math.PI / 6, 300);
          break;
      }

      // Zbierz dane z warstwy
      let layerData = {};
      let layerColors = {};

      switch(layer) {
        case 'top': // Górna warstwa (y = 2)
          layerData = {
            front: [gameData.front[0][2], gameData.front[1][2], gameData.front[2][2]],
            right: [gameData.right[0][2], gameData.right[1][2], gameData.right[2][2]],
            back: [gameData.back[0][2], gameData.back[1][2], gameData.back[2][2]],
            left: [gameData.left[0][2], gameData.left[1][2], gameData.left[2][2]]
          };
          layerColors = {
            front: [tileColors.front[0][2], tileColors.front[1][2], tileColors.front[2][2]],
            right: [tileColors.right[0][2], tileColors.right[1][2], tileColors.right[2][2]],
            back: [tileColors.back[0][2], tileColors.back[1][2], tileColors.back[2][2]],
            left: [tileColors.left[0][2], tileColors.left[1][2], tileColors.left[2][2]]
          };
          break;
        case 'middle': // Środkowa warstwa pozioma (y = 1)
          layerData = {
            front: [gameData.front[0][1], gameData.front[1][1], gameData.front[2][1]],
            right: [gameData.right[0][1], gameData.right[1][1], gameData.right[2][1]],
            back: [gameData.back[0][1], gameData.back[1][1], gameData.back[2][1]],
            left: [gameData.left[0][1], gameData.left[1][1], gameData.left[2][1]]
          };
          layerColors = {
            front: [tileColors.front[0][1], tileColors.front[1][1], tileColors.front[2][1]],
            right: [tileColors.right[0][1], tileColors.right[1][1], tileColors.right[2][1]],
            back: [tileColors.back[0][1], tileColors.back[1][1], tileColors.back[2][1]],
            left: [tileColors.left[0][1], tileColors.left[1][1], tileColors.left[2][1]]
          };
          break;
        case 'bottom': // Dolna warstwa (y = 0)
          layerData = {
            front: [gameData.front[0][0], gameData.front[1][0], gameData.front[2][0]],
            left: [gameData.left[0][0], gameData.left[1][0], gameData.left[2][0]],
            back: [gameData.back[0][0], gameData.back[1][0], gameData.back[2][0]],
            right: [gameData.right[0][0], gameData.right[1][0], gameData.right[2][0]]
          };
          layerColors = {
            front: [tileColors.front[0][0], tileColors.front[1][0], tileColors.front[2][0]],
            left: [tileColors.left[0][0], tileColors.left[1][0], tileColors.left[2][0]],
            back: [tileColors.back[0][0], tileColors.back[1][0], tileColors.back[2][0]],
            right: [tileColors.right[0][0], tileColors.right[1][0], tileColors.right[2][0]]
          };
          break;
        case 'left': // Lewa warstwa (x = 0)
          layerData = {
            front: [gameData.front[0][0], gameData.front[0][1], gameData.front[0][2]],
            top: [gameData.top[0][0], gameData.top[0][1], gameData.top[0][2]],
            back: [gameData.back[2][2], gameData.back[2][1], gameData.back[2][0]],
            bottom: [gameData.bottom[0][2], gameData.bottom[0][1], gameData.bottom[0][0]]
          };
          layerColors = {
            front: [tileColors.front[0][0], tileColors.front[0][1], tileColors.front[0][2]],
            top: [tileColors.top[0][0], tileColors.top[0][1], tileColors.top[0][2]],
            back: [tileColors.back[2][2], tileColors.back[2][1], tileColors.back[2][0]],
            bottom: [tileColors.bottom[0][2], tileColors.bottom[0][1], tileColors.bottom[0][0]]
          };
          break;
        case 'center': // Środkowa warstwa pionowa (x = 1)
          layerData = {
            front: [gameData.front[1][0], gameData.front[1][1], gameData.front[1][2]],
            top: [gameData.top[1][0], gameData.top[1][1], gameData.top[1][2]],
            back: [gameData.back[1][2], gameData.back[1][1], gameData.back[1][0]],
            bottom: [gameData.bottom[1][2], gameData.bottom[1][1], gameData.bottom[1][0]]
          };
          layerColors = {
            front: [tileColors.front[1][0], tileColors.front[1][1], tileColors.front[1][2]],
            top: [tileColors.top[1][0], tileColors.top[1][1], tileColors.top[1][2]],
            back: [tileColors.back[1][2], tileColors.back[1][1], tileColors.back[1][0]],
            bottom: [tileColors.bottom[1][2], tileColors.bottom[1][1], tileColors.bottom[1][0]]
          };
          break;
        case 'right': // Prawa warstwa (x = 2)
          layerData = {
            front: [gameData.front[2][0], gameData.front[2][1], gameData.front[2][2]],
            bottom: [gameData.bottom[2][0], gameData.bottom[2][1], gameData.bottom[2][2]],
            back: [gameData.back[0][2], gameData.back[0][1], gameData.back[0][0]],
            top: [gameData.top[2][2], gameData.top[2][1], gameData.top[2][0]]
          };
          layerColors = {
            front: [tileColors.front[2][0], tileColors.front[2][1], tileColors.front[2][2]],
            bottom: [tileColors.bottom[2][0], tileColors.bottom[2][1], tileColors.bottom[2][2]],
            back: [tileColors.back[0][2], tileColors.back[0][1], tileColors.back[0][0]],
            top: [tileColors.top[2][2], tileColors.top[2][1], tileColors.top[2][0]]
          };
          break;
      }

      // Obróć dane
      const faces = Object.keys(layerData);
      const rotatedData = {};
      const rotatedColors = {};

      for (let i = 0; i < faces.length; i++) {
        const currentFace = faces[i];
        const nextFace = faces[(i + direction + faces.length) % faces.length];
        rotatedData[currentFace] = layerData[nextFace];
        rotatedColors[currentFace] = layerColors[nextFace];
      }

      // Zastosuj obrócone dane
      switch(layer) {
        case 'top':
          for (let x = 0; x < 3; x++) {
            gameData.front[x][2] = rotatedData.front[x];
            gameData.right[x][2] = rotatedData.right[x];
            gameData.back[x][2] = rotatedData.back[x];
            gameData.left[x][2] = rotatedData.left[x];
            tileColors.front[x][2] = rotatedColors.front[x];
            tileColors.right[x][2] = rotatedColors.right[x];
            tileColors.back[x][2] = rotatedColors.back[x];
            tileColors.left[x][2] = rotatedColors.left[x];
          }
          rotateFace('top', direction);
          break;
        case 'middle':
          for (let x = 0; x < 3; x++) {
            gameData.front[x][1] = rotatedData.front[x];
            gameData.right[x][1] = rotatedData.right[x];
            gameData.back[x][1] = rotatedData.back[x];
            gameData.left[x][1] = rotatedData.left[x];
            tileColors.front[x][1] = rotatedColors.front[x];
            tileColors.right[x][1] = rotatedColors.right[x];
            tileColors.back[x][1] = rotatedColors.back[x];
            tileColors.left[x][1] = rotatedColors.left[x];
          }
          break;
        case 'bottom':
          for (let x = 0; x < 3; x++) {
            gameData.front[x][0] = rotatedData.front[x];
            gameData.left[x][0] = rotatedData.left[x];
            gameData.back[x][0] = rotatedData.back[x];
            gameData.right[x][0] = rotatedData.right[x];
            tileColors.front[x][0] = rotatedColors.front[x];
            tileColors.left[x][0] = rotatedColors.left[x];
            tileColors.back[x][0] = rotatedColors.back[x];
            tileColors.right[x][0] = rotatedColors.right[x];
          }
          rotateFace('bottom', direction);
          break;
        case 'left':
          for (let y = 0; y < 3; y++) {
            gameData.front[0][y] = rotatedData.front[y];
            gameData.top[0][y] = rotatedData.top[y];
            gameData.back[2][2-y] = rotatedData.back[y];
            gameData.bottom[0][2-y] = rotatedData.bottom[y];
            tileColors.front[0][y] = rotatedColors.front[y];
            tileColors.top[0][y] = rotatedColors.top[y];
            tileColors.back[2][2-y] = rotatedColors.back[y];
            tileColors.bottom[0][2-y] = rotatedColors.bottom[y];
          }
          rotateFace('left', direction);
          break;
        case 'center':
          for (let y = 0; y < 3; y++) {
            gameData.front[1][y] = rotatedData.front[y];
            gameData.top[1][y] = rotatedData.top[y];
            gameData.back[1][2-y] = rotatedData.back[y];
            gameData.bottom[1][2-y] = rotatedData.bottom[y];
            tileColors.front[1][y] = rotatedColors.front[y];
            tileColors.top[1][y] = rotatedColors.top[y];
            tileColors.back[1][2-y] = rotatedColors.back[y];
            tileColors.bottom[1][2-y] = rotatedColors.bottom[y];
          }
          break;
        case 'right':
          for (let y = 0; y < 3; y++) {
            gameData.front[2][y] = rotatedData.front[y];
            gameData.bottom[2][y] = rotatedData.bottom[y];
            gameData.back[0][2-y] = rotatedData.back[y];
            gameData.top[2][2-y] = rotatedData.top[y];
            tileColors.front[2][y] = rotatedColors.front[y];
            tileColors.bottom[2][y] = rotatedColors.bottom[y];
            tileColors.back[0][2-y] = rotatedColors.back[y];
            tileColors.top[2][2-y] = rotatedColors.top[y];
          }
          rotateFace('right', direction);
          break;
      }

      updateTiles();
    }

    // Obrót samej ściany (macierz 3x3)
    function rotateFace(face, direction) {
      const oldData = [];
      const oldColors = [];

      // Zbierz dane
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          oldData[x * 3 + y] = gameData[face][x][y];
          oldColors[x * 3 + y] = tileColors[face][x][y];
        }
      }

      // Obróć macierz 3x3
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          if (direction === 1) { // W prawo
            gameData[face][x][y] = oldData[(2 - y) * 3 + x];
            tileColors[face][x][y] = oldColors[(2 - y) * 3 + x];
          } else { // W lewo
            gameData[face][x][y] = oldData[y * 3 + (2 - x)];
            tileColors[face][x][y] = oldColors[y * 3 + (2 - x)];
          }
        }
      }
    }

    // Mechanika 2048 - przesuwanie i łączenie kafelków
    function moveLeft() {
      if (isAnimating) return;
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        const colors = [];

        // Zbierz niepuste kafelki z wiersza
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
            colors.push(tileColors[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
            colors.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z prawej strony
        while (row.length < 3) {
          row.push(0);
          colors.push(faceColors[face]);
        }

        // Sprawdź czy coś się zmieniło
        for (let x = 0; x < 3; x++) {
          if (gameData[face][x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][x][y] = row[x];
          tileColors[face][x][y] = colors[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    function moveRight() {
      if (isAnimating) return;
      let moved = false;
      const face = activeFace;

      for (let y = 0; y < 3; y++) {
        const row = [];
        const colors = [];

        // Zbierz niepuste kafelki z wiersza (od prawej)
        for (let x = 2; x >= 0; x--) {
          if (gameData[face][x][y] > 0) {
            row.push(gameData[face][x][y]);
            colors.push(tileColors[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < row.length - 1; i++) {
          if (row[i] === row[i + 1]) {
            row[i] *= 2;
            score += row[i];
            row.splice(i + 1, 1);
            colors.splice(i + 1, 1);
          }
        }

        // Wypełnij wiersz zerami z lewej strony
        while (row.length < 3) {
          row.push(0);
          colors.push(faceColors[face]);
        }

        // Sprawdź czy coś się zmieniło i ustaw od prawej
        for (let x = 0; x < 3; x++) {
          if (gameData[face][2-x][y] !== row[x]) {
            moved = true;
          }
          gameData[face][2-x][y] = row[x];
          tileColors[face][2-x][y] = colors[x];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    function moveUp() {
      if (isAnimating) return;
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        const colors = [];

        // Zbierz niepuste kafelki z kolumny
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
            colors.push(tileColors[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
            colors.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z dołu
        while (col.length < 3) {
          col.push(0);
          colors.push(faceColors[face]);
        }

        // Sprawdź czy coś się zmieniło
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][y] = col[y];
          tileColors[face][x][y] = colors[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    function moveDown() {
      if (isAnimating) return;
      let moved = false;
      const face = activeFace;

      for (let x = 0; x < 3; x++) {
        const col = [];
        const colors = [];

        // Zbierz niepuste kafelki z kolumny (od dołu)
        for (let y = 2; y >= 0; y--) {
          if (gameData[face][x][y] > 0) {
            col.push(gameData[face][x][y]);
            colors.push(tileColors[face][x][y]);
          }
        }

        // Połącz sąsiadujące kafelki o tej samej wartości
        for (let i = 0; i < col.length - 1; i++) {
          if (col[i] === col[i + 1]) {
            col[i] *= 2;
            score += col[i];
            col.splice(i + 1, 1);
            colors.splice(i + 1, 1);
          }
        }

        // Wypełnij kolumnę zerami z góry
        while (col.length < 3) {
          col.push(0);
          colors.push(faceColors[face]);
        }

        // Sprawdź czy coś się zmieniło i ustaw od dołu
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][2-y] !== col[y]) {
            moved = true;
          }
          gameData[face][x][2-y] = col[y];
          tileColors[face][x][2-y] = colors[y];
        }
      }

      if (moved) {
        updateTiles();
        setTimeout(addRandomTile, 200);
      }
    }

    // Dodawanie losowego kafelka
    function addRandomTile() {
      const face = activeFace;
      const emptyTiles = [];

      // Znajdź puste miejsca na aktywnej ścianie
      for (let x = 0; x < 3; x++) {
        for (let y = 0; y < 3; y++) {
          if (gameData[face][x][y] === 0) {
            emptyTiles.push({x, y});
          }
        }
      }

      if (emptyTiles.length > 0) {
        const randomTile = emptyTiles[Math.floor(Math.random() * emptyTiles.length)];
        const value = Math.random() < 0.9 ? 2 : 4;
        gameData[face][randomTile.x][randomTile.y] = value;
        // Kolor ściany pozostaje bez zmian
        updateTiles();
      }
    }

    // Sterowanie klawiaturą
    document.addEventListener('keydown', (e) => {
      switch(e.code) {
        // Mechanika 2048 - strzałki
        case 'ArrowLeft':
          e.preventDefault();
          moveLeft();
          break;
        case 'ArrowRight':
          e.preventDefault();
          moveRight();
          break;
        case 'ArrowUp':
          e.preventDefault();
          moveUp();
          break;
        case 'ArrowDown':
          e.preventDefault();
          moveDown();
          break;
        // Obroty warstw kostki
        case 'KeyQ':
          rotateLayer('top', 1);
          break;
        case 'KeyE':
          rotateLayer('top', -1);
          break;
        case 'KeyW':
          rotateLayer('middle', 1);
          break;
        case 'KeyS':
          rotateLayer('middle', -1);
          break;
        case 'KeyZ':
          rotateLayer('bottom', 1);
          break;
        case 'KeyX':
          rotateLayer('bottom', -1);
          break;
        case 'KeyA':
          rotateLayer('left', 1);
          break;
        case 'KeyD':
          rotateLayer('right', 1);
          break;
        case 'KeyC':
          rotateLayer('center', 1);
          break;
        // Cyfry 1-6 dla szybkiej zmiany aktywnej ściany
        case 'Digit1':
          if (activeFace !== 'front') { activeFace = 'front'; rotateCubeToFace('front'); updateTiles(); }
          break;
        case 'Digit2':
          if (activeFace !== 'back') { activeFace = 'back'; rotateCubeToFace('back'); updateTiles(); }
          break;
        case 'Digit3':
          if (activeFace !== 'left') { activeFace = 'left'; rotateCubeToFace('left'); updateTiles(); }
          break;
        case 'Digit4':
          if (activeFace !== 'right') { activeFace = 'right'; rotateCubeToFace('right'); updateTiles(); }
          break;
        case 'Digit5':
          if (activeFace !== 'top') { activeFace = 'top'; rotateCubeToFace('top'); updateTiles(); }
          break;
        case 'Digit6':
          if (activeFace !== 'bottom') { activeFace = 'bottom'; rotateCubeToFace('bottom'); updateTiles(); }
          break;
      }
    });

    // Funkcja obrotu kostki do pokazania wybranej ściany
    function rotateCubeToFace(face) {
      if (isAnimating) return;

      const rotations = {
        front: { x: 0, y: 0 },
        back: { x: 0, y: Math.PI },
        left: { x: 0, y: -Math.PI/2 },
        right: { x: 0, y: Math.PI/2 },
        top: { x: -Math.PI/2, y: 0 },
        bottom: { x: Math.PI/2, y: 0 }
      };

      const target = rotations[face];
      if (!target) return;

      isAnimating = true;
      const startX = cubeGroup.rotation.x;
      const startY = cubeGroup.rotation.y;
      const startTime = Date.now();
      const duration = 800;

      function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Smooth easing
        const easeInOut = progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;

        cubeGroup.rotation.x = startX + (target.x - startX) * easeInOut;
        cubeGroup.rotation.y = startY + (target.y - startY) * easeInOut;

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          isAnimating = false;
        }
      }

      animate();
    }

    // Obsługa kliknięć na podgląd ścian
    document.addEventListener('click', (e) => {
      const facePreview = e.target.closest('.face-preview');
      if (facePreview) {
        const face = facePreview.getAttribute('data-face');
        if (face && face !== activeFace) {
          activeFace = face;
          rotateCubeToFace(face);
          updateTiles();
        }
      }
    });

    // Inicjalizacja
    createCubeStructure();
    createTiles();
    updateTiles();
    
    console.log('Kostka utworzona!');
    
    // Animacja
    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }
    animate();
  </script>
</body>
</html>
